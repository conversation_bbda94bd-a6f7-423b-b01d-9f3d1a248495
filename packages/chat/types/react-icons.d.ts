declare module 'react-icons/fa' {
  import { ComponentType, SVGAttributes } from 'react';
  
  export interface IconBaseProps extends SVGAttributes<SVGElement> {
    size?: string | number;
    color?: string;
    title?: string;
  }
  
  export type IconType = ComponentType<IconBaseProps>;
  
  export const FaArrowLeft: IconType;
  export const FaMicrophone: IconType;
  export const FaPaperclip: IconType;
  export const FaRegSmile: IconType;
  export const FaPaperPlane: IconType;
  export const FaSearch: IconType;
  export const FaFileAlt: IconType;
  export const FaRegBell: IconType;
  export const FaUsers: IconType;
  export const FaCog: IconType;
  export const FaSignOutAlt: IconType;
  export const FaCalendarAlt: IconType;
  export const FaHeadset: IconType;
  export const FaPlus: IconType;
  export const FaRegBell: IconType;
  export const FaTimes: IconType;
}

declare module 'react-icons/md' {
  import { ComponentType, SVGAttributes } from 'react';
  
  export interface IconBaseProps extends SVGAttributes<SVGElement> {
    size?: string | number;
    color?: string;
    title?: string;
  }
  
  export type IconType = ComponentType<IconBaseProps>;
  
  export const MdOutlineCancel: IconType;
}