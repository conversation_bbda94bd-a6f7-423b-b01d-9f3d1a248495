# 🌈 Customizing Theme for `<Chat />` Component

This guide explains how to apply a custom theme to the `<Chat />` component in your React or Next.js application.


---

## ✨ Overview

The `<Chat />` component supports theming via a `theme` prop. By passing a theme object, you can easily customize the appearance of the chat UI, including background colors, text colors, borders, and more.

---

## 🧠 Theme Object Structure

The theme object can include the following keys:

| Key                | Description                                 | Type    | Example                      |
|--------------------|---------------------------------------------|---------|------------------------------|
| `bgColor`          | Background color of the chat container      | string  | `"#1A1A1A"`                  |
| `hoverColor`       | Color used on hoverable elements            | string  | `"#FF5722"`                  |
| `textColor`        | Text color throughout the chat UI           | string  | `"#FEFEFE"`                  |
| `borderColor`      | Border color for UI elements                | string  | `"#CCCCCC33"`                |
| `activeChatBorder` | Highlight color for the active chat bubble  | string  | `"rgb(47 165 102)"`          |
| `accentColor`      | Accent color used in the chat UI            | string  | `"rgb(39 37 37)"`            |
| `secondaryColor`   | Secondary highlight or button color         | string  | `"rgb(47 165 102)"`          |
| `height`           | Height of the chat component                | string  | `"100vh"`                    |

---

## 🚀 Usage Example

Here’s how you can use the `theme` prop to customize the chat appearance:

```tsx
import Chat from "./components/Chat"; // Adjust the path as needed

export default function Home() {
  const myCustomTheme = {
    bgColor: "#1A1A1A",
    hoverColor: "#FF5722",
    textColor: "#FEFEFE",
    borderColor: "#CCCCCC33",
    activeChatBorder: "rgb(47 165 102)",
    accentColor: "rgb(39 37 37)",
    secondaryColor: "rgb(47 165 102)",
    height: "100vh"
  };

  return <Chat theme={myCustomTheme} />;
}
