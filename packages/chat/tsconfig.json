{"include": ["src", "types"], "compilerOptions": {"module": "node16", "lib": ["dom", "dom.iterable", "esnext"], "target": "esnext", "importHelpers": true, "declaration": true, "sourceMap": true, "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node16", "baseUrl": "./", "paths": {"*": ["src/*", "node_modules/*"]}, "jsx": "react-jsx", "esModuleInterop": true, "allowJs": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}}