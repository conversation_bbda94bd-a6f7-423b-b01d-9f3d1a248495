"use client";

import Layout from "components/Layout";
import { ThemeProvider, useTheme } from "context/themeContext";
import { ChatProvider } from "@sparkstrand/chat-api-client/context";
import { FC } from "react";
import { SocketClientOptions } from "@sparkstrand/chat-api-client/types";

const Chat: FC<{
  theme: {
    bgColor: string;
    hoverColor: string;
    textColor: string;
    borderColor: string;
    activeChatBorder: string;
    accentColor: string;
    secondaryColor: string;
    height: string;
  };
  isFull: boolean;
  chatOptions: SocketClientOptions;
  roomID?: string
  onClose?: () => void;
}> = ({ theme, isFull, chatOptions, onClose, roomID }) => {
  return (
    <ChatProvider options={chatOptions}>
      <ThemeProvider initialTheme={theme}>
        <Layout onClose={onClose} isFull={isFull} roomID={roomID} />
      </ThemeProvider>
    </ChatProvider>
  );
};

// Component exports
export { default as ChatList } from "./components/ChatList/ChatList";
export { default as ChatContainer } from "./components/ChatContainer";
export { default as Message } from "./components/Message";
export { default as ChatSideBar } from "./components/ChatSideBar";
export { default as EventCards } from "./components/ChatList/EventCards";
export { default as ChatTabMenu } from "./components/ChatTabMenu";
export { default as ChatInputField } from "./components/chatInputField";
export { default as ChatHeader } from "./components/ChatHeader";
export { default as SearchInput } from "./components/searchInput";
export { default as SideBar } from "./components/SideBar";
export { default as TopBar } from "./components/TopBar";
export { default as MessageView } from "./components/Layout/MessageView";

// Context exports
export { useTheme, ThemeProvider };

// Default export
export default Chat;
