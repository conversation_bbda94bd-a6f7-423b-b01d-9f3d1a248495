"use client";
import { useTheme } from "context/themeContext";
import React, {FormEvent} from "react";
import { FaSearch } from "react-icons/fa";

interface SearchInputProps {
  placeholder?: string;
  onSearch: (query: string) => void;
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = "Search for events, chats....",
  onSearch,
}) => {
  const [query, setQuery] = React.useState("");
  const {theme} = useTheme();

  const handleSearch = (event: FormEvent): void => {
    event.preventDefault();
    onSearch(query);
  };

  return (
    <form onSubmit={handleSearch} className="relative w-full max-w-md">
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={placeholder}
        className="block w-full rounded-full border border-gray-300 shadow-md pl-4 pr-10 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        aria-label="Search"
        style={{color: theme.textColor}}
      />
      <button
        type="submit"
        className="absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <FaSearch className="h-5 w-5" aria-hidden="true" style={{color: theme.textColor}}/>
      </button>
    </form>
  );
};

export default SearchInput;
