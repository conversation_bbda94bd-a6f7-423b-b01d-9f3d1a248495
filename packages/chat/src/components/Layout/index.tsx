import TopBar from "components/TopBar";
import { useEffect, useState } from "react";
import MessageView from "./MessageView";
import ChatSideBar from "components/ChatSideBar";
import { useTheme } from "context/themeContext";
import { useChat } from "@sparkstrand/chat-api-client/context";

const Layout: React.FC<{ isFull: boolean; onClose?: () => void; roomID?: string }> = ({
  isFull,
  onClose,
  roomID
}) => {
  const { theme } = useTheme();
  const { isConnected, login, userId } = useChat();
  const [showSideBar, setShowSideBar] = useState(false);

  const loginUser = async () => {
    if (userId && !isConnected) {
      await login(userId).catch((err) =>
        console.log("Login failed:*****", err)
      );
    }
  };

  useEffect(() => {
    loginUser();
  }, [userId, isConnected]);

  return (
    <>
      <div
        className={"w-full overflow-hidden flex relative"}
        style={{ height: theme.height, background: theme.bgColor }}
      >
        <div
          className={`${!showSideBar ? "w-full" : "flex-1"}  flex-col overflow-x-hidden`}
        >
          {isFull && (
            <TopBar
              setShowSideBar={setShowSideBar}
              roomID={roomID as string}
              onClose={onClose && onClose}
            />
          )}

          <>
            <MessageView
              isFull={isFull}
              roomID={roomID as string}
            />
          </>
        </div>
        {isFull ? (
          <ChatSideBar
            onClose={() => setShowSideBar(false)}
            roomID={roomID as string}
            showSideBar={showSideBar}
            content={{
              eventInfo: "Event Room Info",
              room: "Room",
              attendies: "Attendee(s)",
              description: "Description",
              notifications: "Notifications",
              medias: "Media",
              files: "Files",
              member: "Members",
              userType: "Moderator",
            }}
          />
        ) : (
          <></>
        )}
      </div>
    </>
  );
};

export default Layout;
