import ChatContainer from "components/ChatContainer";
import ChatInput<PERSON>ield from "components/chatInputField";
import { useTheme } from "context/themeContext";
import {
  useChat,
  useChatMessage,
  useChatRoom,
} from "@sparkstrand/chat-api-client/context";
import {useEffect } from "react";
import { IRoom, RoomType } from "@sparkstrand/chat-api-client/types";

const MessageView: React.FC<{
  roomID: string;
  isFull: boolean;
}> = ({ roomID, isFull }) => {
  const {
    getRoomDataById,
    currentRoomData,
    createRoom,
    rooms,
    emitGetListOfGuestRooms,
  } = useChatRoom();
  const { userId } = useChat();

  useEffect(() => {
    if (!isFull && rooms.length > 0 && userId) {
      let room = rooms?.find(
        (room: IRoom) => room.name == "Chat Supports " + userId
      );
      if (room) {
      } else {
        createRoom({
          name: "Chat Supports " + userId,
          type: RoomType.ANONYMOUS,
        });
        emitGetListOfGuestRooms();
      }
    }
  }, [userId, rooms]);

  useEffect(() => {
    if (roomID) {
      getRoomDataById(roomID);
    }
  }, [roomID, rooms]);

  const { messages, sendMessage } = useChatMessage();
  const { theme } = useTheme();

  return (
    <div className="w-full flex" style={{ background: theme.bgColor }}>
      <div className="flex-1 pb-2" style={{ background: theme.bgColor }}>
        <ChatContainer
          messages={[...(currentRoomData?.messages || []), ...messages]}
          isFull={isFull}
          roomID={roomID}
        />
        <ChatInputField sendMessage={sendMessage} roomId={roomID} />
      </div>
    </div>
  );
};

export default MessageView;
