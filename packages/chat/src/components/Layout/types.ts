export interface IMessage {
  id: string;
  content: string;
  roomId: string;
  senderId: string;
  timestamp: string;
  read?: boolean;
  isPinned?: boolean;
  isAnswered?: boolean;
  isEncrypted?: boolean;
  status?: string;
  parentId?: string | null;
  edited?: boolean;
  analyticId?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  from?: string;
  to?: string;
  files?: Array<{
    filename: string;
    fileUrl: string;
  }>;
}

export interface MessageContextType {
  sendMessage: (message: IMessage) => void;
  markMessageRead: (messageId: string) => void;
  messages: IMessage[];
}
