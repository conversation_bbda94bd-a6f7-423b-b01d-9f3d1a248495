import { useChatRoom, } from "@sparkstrand/chat-api-client/context";
import { IRoom } from "@sparkstrand/chat-api-client/types";
import ChatList from "components/ChatList/ChatList";
import ChatTabMenu from "components/ChatTabMenu";
import Pagination from "components/pagination";
import SearchInput from "components/searchInput";
import { useTheme } from "context/themeContext";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";

const ListUI: React.FC<{ setRoomID: Dispatch<SetStateAction<string>> }> = ({
  setRoomID,
}) => {
  const { theme } = useTheme();
  const { rooms, emitGetListOfGuestRooms } = useChatRoom();
  const roomsPerPage = 2;
  const [totalPages, setTotalPages] = useState(1);
  const [eventsToShow, setEventsToShow] = useState("past");
  const [pastEvents, setPastEvents] = useState<IRoom[]>([]);
  const [pastLength, setPastLength] = useState<number>(0);
  const [upcomingLength, setUpcomingLength] = useState<number>(0);
  const [upComingEvents, setUpComingEvents] = useState<IRoom[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [roomsToShow, setRoomsToShow] = useState<IRoom[]>([]);

  useEffect(() => {
    if (typeof window != undefined) {
      emitGetListOfGuestRooms();
    }
  }, []);

  const onPageChange = (events: IRoom[]) => {
    let roomsToEdit = [...events];
    setRoomsToShow(
      roomsToEdit.slice(
        currentPage * roomsPerPage - roomsPerPage,
        roomsPerPage * currentPage
      )
    );
  };

  const filterEvents = () => {
    const roomsToFilter = [...rooms];
    const currentTime = Date.now();
    const pastEvent = roomsToFilter.filter(
      (room) =>
        room?.metaData?.eventDate &&
        new Date(room?.metaData?.eventDate).getTime() < currentTime
    );
    const upcomingEvent = roomsToFilter.filter(
      (room) =>
        room?.metaData?.eventDate &&
        new Date(room?.metaData?.eventDate).getTime() > currentTime
    );
    setPastLength(pastEvent.length);
    setUpcomingLength(upcomingEvent.length);
    setUpComingEvents(upcomingEvent);
    setPastEvents(pastEvent);
    setTotalPages(Math.ceil(pastEvent.length / roomsPerPage));
    onPageChange(pastEvent);
  };

  // useEffect(() => {
  //   if (rooms.length > 0) {
  //     onPageChange();
  //   }
  // }, [currentPage]);

  useEffect(() => {
    if (rooms.length > 0) {
      filterEvents();
    }
  }, [rooms]);

  useEffect(() => {
    if (rooms.length > 0) {
      if (eventsToShow == "past") {
        setTotalPages(Math.ceil(pastEvents.length / roomsPerPage));
        onPageChange(pastEvents);
      }
      if (eventsToShow == "upcoming") {
        setTotalPages(Math.ceil(upComingEvents.length / roomsPerPage));
        onPageChange(upComingEvents);
      }
    }
  }, [eventsToShow, currentPage]);

  return (
    <div
      className="w-full items-center overflow-y-auto py-6 px-24 flex flex-col gap-6 [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden"
      style={{
        background: theme.bgColor,
        height: `calc(${theme.height} - 60px)`,
      }}
    >
      <SearchInput
        onSearch={() => {}}
        placeholder="Search for events, chats...."
      />
      <ChatTabMenu
        setEventsToShow={setEventsToShow}
        eventsToShow={eventsToShow}
      />
      <ChatList
        title="Upcoming Events"
        length={eventsToShow == "past" ? pastLength : upcomingLength}
        eventText="Event Location"
        setRoomID={setRoomID}
        rooms={roomsToShow}
      />
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        nextText="Next"
        previousText="Prev"
        setCurrentPage={setCurrentPage}
      />
    </div>
  );
};

export default ListUI;
