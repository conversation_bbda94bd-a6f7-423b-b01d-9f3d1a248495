"use client";

import { useEffect, useState } from "react";

import { MdOutlineCancel } from "react-icons/md";
import { FaFileAlt, FaRegBell, FaUsers } from "react-icons/fa";
import ImageSliderModal from "./ImageModal/ImageSliderModal";
import DocumentSliderModal from "./ImageModal/DocumentSliderModal";
import { useTheme } from "context/themeContext";
import {
  useChatRoom,
  useFileUpload,
} from "@sparkstrand/chat-api-client/context";
import { IRoomGuests, IRoomMedia } from "@sparkstrand/chat-api-client/types";
import MediaItem from "./MediaItem";

const fallbackImage =
  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='%23ddd'%3E%3Crect width='100' height='100'/%3E%3C/svg%3E";



interface ChatSideBarProps {
  content: {
    eventInfo: string;
    room: string;
    attendies: string;
    description: string;
    notifications: string;
    medias: string;
    files: string;
    member: string;
    userType: string;
  };
  roomID: string;
  onClose: () => void;
  showSideBar: boolean;
}

type RawUsers =
  | { admins: IRoomGuests[]; moderators: IRoomGuests[]; members: IRoomGuests[] }
  | undefined;

const ChatSideBar = ({
  content,
  onClose,
  roomID,
  showSideBar,
}: ChatSideBarProps) => {
  const [switchOn, setSwitchOn] = useState(false);
  const [mediaChoice, setMediaChoice] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDocModalOpen, setIsDocModalOpen] = useState<boolean>(false);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [mediaToShow, setMediaToShow] = useState<any[]>([]);
  const [newFiles, setNewFiles] = useState<any[]>([]);
  const { theme } = useTheme();
  const {
    attendies,
    description,
    eventInfo,
    files,
    medias,
    member,
    notifications,
    room,
  } = content;

  const { getRoomDataById, currentRoomData, currentRoomMedia, getRoomMedia } =
    useChatRoom();
  const { getSignedUrl } = useFileUpload();

  const toggleSwitch = () => {
    setSwitchOn(!switchOn);
  };

  useEffect(() => {
    if (roomID) {
      getRoomDataById(roomID);
      getRoomMedia(roomID);
    }
  }, [roomID]);

  const sortFile = (isImage: boolean) => {
    let files = [];
    if (currentRoomMedia?.length > 0) {
      if (isImage) {
        files = [
          ...currentRoomMedia.filter((file: IRoomMedia) =>
            file.fileType.includes("image")
          ),
        ];
      } else {
        files = [
          ...currentRoomMedia.filter(
            (file: IRoomMedia) => !file.fileType.includes("image")
          ),
        ];
      }
    } else {
      return [];
    }
    return files;
  };

  function transformUserData(data: RawUsers) {
    const extract = (users: IRoomGuests[] | undefined, type: string) =>
      (users ?? []).map((user) => ({
        name: user.name,
        avatar: user.avatar.fileUrl || "",
        userType: type,
        userName: user.username,
        id: user.id,
        status: user.status,
        lastSeen: user.lastSeenAt,
      }));

    const admin = extract(data?.admins, "ADMIN");
    const moderator = extract(data?.moderators, "MODERATOR");
    const members = extract(data?.members, "MEMBER");

    return [admin, moderator, members].flat(2);
  }

  const getAllUrl = async (file: IRoomMedia) => {
    const alreadyExists = newFiles.some((f) => f.id === file.id);
    if (alreadyExists) return;

    const url = await getSignedUrl(file.id, {
      baseUrl: process.env.NEXT_PUBLIC_CHAT_SERVER_URL,
      inline: true,
    });

    setNewFiles((prev) => [...prev, { url, id: file.id }]);
  };



  return (
    <div
      className={`w-full md:max-w-[356px] h-screen overflow-y-auto shadow-lg ${!showSideBar ? "absolute top-0 right-[-200%]" : "top-0 right-0 relative"}`}
      style={{ background: theme.accentColor }}
    >
      <ImageSliderModal
        images={mediaToShow}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        selectedIndex={selectedIndex}
        setSelectedIndex={setSelectedIndex}
      />
      <DocumentSliderModal
        documents={mediaToShow}
        isOpen={isDocModalOpen}
        onClose={() => setIsDocModalOpen(false)}
        selectedIndex={selectedIndex}
        setSelectedIndex={setSelectedIndex}
      />

      {/* Header */}
      <div className="w-full h-[58.71px] p-5 flex justify-between items-center border-b border-gray-500">
        <h2 className="text-xl font-bold" style={{ color: theme.textColor }}>
          {eventInfo}
        </h2>
        <MdOutlineCancel
          size={24}
          className="cursor-pointer"
          color={theme.textColor}
          onClick={onClose}
        />
      </div>

      {/* Info */}
      <div className="w-full p-5 flex flex-col items-center gap-12">
        {/* Room Info */}
        <div className="flex flex-col gap-3 items-center w-full">
          <div className="w-[100px] h-[100px]">
            <img
              src={`${currentRoomData?.avatar || fallbackImage}`}
              alt="Room"
              className="w-full h-full object-cover rounded-md"
            />
          </div>
          <h4
            className="text-base font-bold"
            style={{ color: theme.textColor }}
          >
            {currentRoomData?.name}
          </h4>
          <p className="text-xs" style={{ color: theme.textColor }}>
            {room} • {currentRoomData?.membersCount} {attendies}
          </p>
        </div>

        {/* Description */}
        <div className="w-full flex flex-col gap-2 items-start py-3">
          <div className="flex gap-2 items-center">
            <FaFileAlt
              size={16}
              className=""
              style={{ color: theme.textColor }}
            />
            <h4
              className="text-sm font-semibold "
              style={{ color: theme.textColor }}
            >
              {description}
            </h4>
          </div>
          <p className="text-xs" style={{ color: theme.textColor }}>
            {currentRoomData?.description}
          </p>
        </div>

        {/* Notifications */}
        <div className="w-full flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <FaRegBell
              size={16}
              className=""
              style={{ color: theme.textColor }}
            />
            <h4
              className="text-sm font-semibold"
              style={{ color: theme.textColor }}
            >
              {notifications}
            </h4>
          </div>
          <div
            onClick={toggleSwitch}
            className={`w-10 h-5 rounded-full px-1 cursor-pointer flex items-center ${
              switchOn
                ? "bg-[#2FA566] justify-end"
                : "bg-gray-400 justify-start"
            }`}
          >
            <div className="w-[14px] h-[14px] bg-gray-100 rounded-full" />
          </div>
        </div>

        {/* Media */}
        <div className="w-full">
          <div className="w-full h-12 rounded-md bg-gray-300 flex mb-5">
            <div
              onClick={() => setMediaChoice(1)}
              className={`w-1/2 h-full cursor-pointer flex gap-5 px-3 items-center rounded-md 
             
              `}
              style={{
                background: mediaChoice === 1 ? theme.secondaryColor : "",
              }}
            >
              <h4
                className="text-sm font-semibold  m-0"
                style={{ color: theme.textColor }}
              >
                {medias}
              </h4>
              <div
                className="w-5 h-5 bg-white rounded-full text-xs flex items-center justify-center "
                style={{ color: theme.secondaryColor }}
              >
                {sortFile(true).length}
              </div>
            </div>
            <div
              onClick={() => setMediaChoice(2)}
              className={`w-1/2 h-full cursor-pointer flex gap-5 px-3 items-center rounded-md `}
              style={{
                background: mediaChoice === 2 ? theme.secondaryColor : "",
              }}
            >
              <h4
                className="text-sm font-semibold  m-0"
                style={{ color: theme.textColor }}
              >
                {files}
              </h4>
              <div
                className="w-5 h-5 bg-white rounded-full text-xs flex items-center justify-center "
                style={{ color: theme.secondaryColor }}
              >
                {sortFile(false).length}
              </div>
            </div>
          </div>

          <div className="w-full flex gap-2 overflow-x-auto pb-2 whitespace-nowrap scrollbar-hide">
            {(mediaChoice === 1
              ? sortFile(true).reverse()
              : sortFile(false).reverse()
            ).map((file, index) => {
              const matchedUrl =
                newFiles.find((f) => f.id === file.id)?.url || "";
              

              return (
                <MediaItem
                  key={file.id}
                  file={file}
                  index={index}
                  type={mediaChoice === 1 ? "image" : "document"}
                  theme={theme}
                  matchedUrl={matchedUrl}
                  onClick={(i) => {

                    setSelectedIndex(i);
                    setMediaToShow([{ ...file, fileUrl: matchedUrl }]);
                    mediaChoice === 1
                      ? setIsModalOpen(true)
                      : setIsDocModalOpen(true);
                  }}
                  onInView={async () => {
                    getAllUrl(file);
                  }}
                />
              );
            })}
          </div>
        </div>

        {/* Members */}
        <div className="w-full flex flex-col gap-3">
          <div className="flex gap-2 items-center">
            <FaUsers
              size={16}
              className=""
              style={{ color: theme.textColor }}
            />
            <h4
              className="text-sm font-semibold "
              style={{ color: theme.textColor }}
            >
              {member} ({currentRoomData?.membersCount})
            </h4>
          </div>
          <div className="flex flex-col gap-4">
            {transformUserData(currentRoomData?.guests as RawUsers)?.map(
              (member, index) => (
                <div
                  key={`${member?.id}-${index}`}
                  className="w-full flex justify-between"
                >
                  <div className="flex gap-2 items-center">
                    <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center font-bold">
                      {member?.name.charAt(0)}
                    </div>
                    <div className="flex flex-col gap-0.5">
                      <h3
                        className="text-sm font-medium m-0"
                        style={{ color: theme.textColor }}
                      >
                        {member?.name}
                      </h3>
                      <p
                        className="text-xs  m-0"
                        style={{ color: theme.textColor }}
                      >
                        {member?.status}
                      </p>
                    </div>
                  </div>
                  {(member?.userType?.toLowerCase() == "moderator" ||
                    member?.userType?.toLowerCase() == "admin") && (
                    <div
                      className="h-10 px-3 text-blue-500 flex items-center justify-center rounded-md"
                      style={{
                        background: theme.secondaryColor,
                        color: theme.textColor,
                      }}
                    >
                      {member?.userType}
                    </div>
                  )}
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatSideBar;
