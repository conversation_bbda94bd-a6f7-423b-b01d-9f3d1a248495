import React, { useEffect } from "react";
import { IRoomMedia } from "@sparkstrand/chat-api-client/types";
import { getFileIcon } from "utils/getFilIcon";
import useIntersectionObserver from "hooks/useIntersectionObserver";

interface MediaItemProps {
  file: IRoomMedia;
  index: number;
  type: "image" | "document";
  theme: {
    textColor: string;
    secondaryColor: string;
  };
  onClick: (index: number) => void;
  matchedUrl?: string;
  onInView?: (index: number) => void; // Callback when visible
}

const MediaItem: React.FC<MediaItemProps> = ({
  file,
  index,
  type,
  theme,
  onClick,
  matchedUrl,
  onInView,
}) => {
  const { elementRef, isIntersecting } = useIntersectionObserver({
    threshold: 0.5,
  });

  useEffect(() => {
    if (isIntersecting) {
      onInView?.(index);
    }
  }, [isIntersecting]);

  if (type === "image") {
    return (
      <div
        ref={elementRef}
        onClick={() => onClick(index)}
        className="w-[100px] h-[100px] flex-shrink-0 cursor-pointer"
      >
        <img
          src={matchedUrl}
          alt={file.filename || `Image ${index + 1}`}
          className="w-full h-full object-cover rounded-md"
        />
      </div>
    );
  }

  return (
    <div
      ref={elementRef}
      onClick={() => onClick(index)}
      className="min-w-[150px] p-4 rounded-md cursor-pointer flex flex-col items-center transition-transform text-center"
      style={{ background: theme.secondaryColor }}
    >
      <span className="text-4xl">{getFileIcon(file.fileType)}</span>
      <span
        className="mt-2 font-bold text-xs break-words"
        style={{ color: theme.textColor }}
      >
        {file.filename}
      </span>
      <span className="mt-1 text-xs" style={{ color: theme.textColor }}>
        {file.fileType.toUpperCase()}
      </span>
    </div>
  );
};

export default MediaItem;
