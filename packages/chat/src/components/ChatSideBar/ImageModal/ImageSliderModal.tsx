import { useEffect } from "react";
import { ImageSliderModalProps } from "./types";

const ImageSliderModal: React.FC<ImageSliderModalProps> = ({
  images,
  isOpen,
  onClose,
  selectedIndex,
  setSelectedIndex,
}) => {
  const goToPrevious = (): void => {
    setSelectedIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const goToNext = (): void => {
    setSelectedIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const goToImage = (index: number): void => {
    setSelectedIndex(index);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent): void => {
      switch (e.key) {
        case "Escape":
          onClose();
          break;
        case "ArrowLeft":
          goToPrevious();
          break;
        case "ArrowRight":
          goToNext();
          break;
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "auto";
    };
  }, [isOpen, selectedIndex, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-90">
      <button
        onClick={onClose}
        className="absolute top-5 right-5 text-white text-3xl px-3 py-1 hover:opacity-70"
        aria-label="Close modal"
      >
        &times;
      </button>

      <div className="relative w-[90%] max-w-[1200px] mx-auto">
        <div className="relative w-full aspect-[16/9]">
          <img
            src={
              images.length > 1
                ? images[selectedIndex]?.fileUrl
                : images[0]?.fileUrl
            }
            alt={
              images.length > 1
                ? images[selectedIndex]?.filename
                : images[0]?.filename
            }
            className="w-full h-full object-contain"
          />
        </div>

        {images.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute top-1/2 left-5 transform -translate-y-1/2 w-12 h-12 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center text-lg hover:bg-opacity-80"
              aria-label="Previous image"
            >
              &larr;
            </button>

            <button
              onClick={goToNext}
              className="absolute top-1/2 right-5 transform -translate-y-1/2 w-12 h-12 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center text-lg hover:bg-opacity-80"
              aria-label="Next image"
            >
              &rarr;
            </button>
          </>
        )}

        <div className="flex justify-center gap-2 mt-5">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => goToImage(index)}
              className={`w-3 h-3 rounded-full ${
                selectedIndex === index ? "bg-white" : "bg-gray-600"
              } hover:bg-gray-400`}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
        </div>

        {images.length > 1 && (
          <div className="text-white text-center mt-3 text-sm">
            {selectedIndex + 1} / {images.length}
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageSliderModal;
