"use client";

import React from "react";
import { IRoomMedia } from "@sparkstrand/chat-api-client/types";
import { getFileIcon } from "utils/getFilIcon";

interface MediaGalleryProps {
  files: IRoomMedia[];
  type: "image" | "document";
  theme: {
    textColor: string;
    secondaryColor: string;
  };
  newFiles?: { id: string; url: string }[];
  onSelect: (index: number) => void;
  elementRef?: React.RefObject<HTMLDivElement>;
}

const MediaGallery: React.FC<MediaGalleryProps> = ({
  files,
  type,
  theme,
  newFiles = [],
  onSelect,
  elementRef,
}) => {
  const isImage = type === "image";

  return (
    <div className="w-full flex gap-2 overflow-x-auto pb-2 whitespace-nowrap scrollbar-hide">
      {files.map((file, index) => {
        const matchedUrl =
          isImage && newFiles?.length
            ? newFiles.find((f) => f.id === file.id)?.url || ""
            : "";

        return isImage ? (
          <div
            key={file.id}
            onClick={() => onSelect(index)}
            ref={index === 0 ? elementRef : undefined}
            className="w-[100px] h-[100px] flex-shrink-0 cursor-pointer"
          >
            <img
              src={matchedUrl}
              alt={file.filename || `Image ${index + 1}`}
              className="w-full h-full object-cover rounded-md"
            />
          </div>
        ) : (
          <div
            key={file.id}
            onClick={() => onSelect(index)}
            ref={index === 0 ? elementRef : undefined}
            className="min-w-[150px] p-4 bg-gray-100 rounded-md cursor-pointer flex flex-col items-center transition-transform text-center"
          >
            <span className="text-4xl">{getFileIcon(file.fileType)}</span>
            <span
              className="mt-2 font-bold text-xs break-words"
              style={{ color: theme.textColor }}
            >
              {file.filename}
            </span>
            <span className="mt-1 text-xs" style={{ color: theme.textColor }}>
              {file.fileType.toUpperCase()}
            </span>
          </div>
        );
      })}
      {/* Sentinel for IntersectionObserver */}
      <div ref={elementRef} className="w-[1px] h-[1px]" />
    </div>
  );
};

export default MediaGallery;
