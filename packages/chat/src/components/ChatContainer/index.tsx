import { FC } from "react";
import Message from "../Message";
import { useTheme } from "context/themeContext";
import { useChat, useUserTyping } from "@sparkstrand/chat-api-client/context";

type Message = {
  profile: string;
  message: string;
  time: string; // ISO 8601 format date string
  user: string;
};

type Sender = {
  id: string;
  name: string;
  username: string;
  avatar: {
    filename: string;
    fileUrl: string;
  };
};

type RawMessage = {
  id: string;
  text: string;
  isPinned: boolean;
  isAnswered: boolean;
  isEncrypted: boolean;
  status: string;
  createdAt: string;
  updatedAt: string;
  read: boolean;
  edited: boolean;
  to: string;
  sender: Sender;
  files: any[];
};

type DayGroupedMessages = {
  day: string;
  messages: RawMessage[];
};

const ChatContainer: FC<{ messages: any; isFull: boolean; roomID: string }> = ({
  messages = [],
  isFull,
  roomID
}) => {
  const {typingUsers } =
    useUserTyping();
  function groupMessagesByDay(messages: RawMessage[]): DayGroupedMessages[] {
    const grouped: Record<string, RawMessage[]> = {};

    messages?.forEach((msg) => {
      const date = new Date(msg.createdAt);
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);

      let dayLabel: string;
      if (date.toDateString() === today.toDateString()) {
        dayLabel = "Today";
      } else if (date.toDateString() === yesterday.toDateString()) {
        dayLabel = "Yesterday";
      } else {
        dayLabel = date.toLocaleDateString(undefined, {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      }

      if (!grouped[dayLabel]) {
        grouped[dayLabel] = [];
      }

      grouped[dayLabel].push(msg);
    });

    return Object.entries(grouped)
      .map(([day, messages]) => ({
        day,
        messages: messages.sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        ),
      }))
      .sort(
        (a, b) =>
          new Date(a.messages[0].createdAt).getTime() -
          new Date(b.messages[0].createdAt).getTime()
      );
  }

  const { userId } = useChat();

  const { theme } = useTheme();

  return (
    <div
      className={`w-full p-2 ${isFull ? "md:p-8" : "md:p-2"} flex flex-col-reverse overflow-y-auto font-sans [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden`}
      style={{
        background: theme.bgColor,
        height: `${isFull ? `calc(${theme.height} - 108px)` : `calc(${theme.height} - 48px)`}`,
      }}
    >
      {groupMessagesByDay(messages || [])
        .reverse()
        ?.map((chatDay, index) => {
          return (
            <div key={index} className="mb-4">
              <div
                className="w-fit px-3 py-1 shadow-[0_1px_2px_rgba(0,0,0,0.3)] font-semibold text-base rounded-lg mx-auto my-5"
                style={{
                  color: theme.textColor,
                  background: theme.accentColor,
                }}
              >
                {chatDay.day}
              </div>
              <div className="space-y-5">
                {chatDay?.messages?.map((msg, msgIndex) => {
                  return (
                    <Message
                      key={msgIndex}
                      isYou={
                        msg?.sender?.id == userId ||
                        userId?.includes(msg?.sender?.username)
                      }
                      message={msg?.text}
                      pic={msg?.sender?.avatar?.fileUrl}
                      user={msg?.sender?.username}
                      time={msg?.createdAt}
                      files={msg?.files}
                      compact={!isFull}
                    />
                  );
                })}
                <div className="mb-2 text-sm text-gray-500">
                  {typingUsers[roomID]?.length > 0 &&
                    `${typingUsers[roomID].join(", ")} ${typingUsers[roomID].length > 1 ? "are" : "is"} typing...`}
                </div>
              </div>
            </div>
          );
        })}
    </div>
  );
};

export default ChatContainer;
