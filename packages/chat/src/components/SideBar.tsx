import { useTheme } from "context/themeContext";
import React, { useState, ReactN<PERSON>, Dispatch, SetStateAction } from "react";
import { FaCog, FaSignOutAlt, FaCalendarAlt, FaHeadset } from "react-icons/fa";

interface NavButtonProps {
  label: string;
  icon: ReactNode;
  onClick?: () => void;
}

interface SideBarProps {
  profileImageSrc?: string;
  userName?: string;
  userId?: string;
  navButtons?: NavButtonProps[];
  footerButtons?: NavButtonProps[];
  setRoomID: Dispatch<SetStateAction<string>>;
}

const ThemedButton = ({ label, icon, onClick }: NavButtonProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const { theme } = useTheme();

  return (
    <button
      style={{
        color: theme.textColor,
        backgroundColor: isHovered ? theme.secondaryColor : "transparent",
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
      className="flex justify-center items-center p-2 transition-all cursor-pointer rounded-lg"
    >
      <span className="mr-2">{icon}</span>
      {label}
    </button>
  );
};

const SideBar: React.FC<SideBarProps> = ({
  profileImageSrc = "",
  userName = "",
  userId = "",
  setRoomID
}) => {
  const { theme } = useTheme();

  return (
    <div
      className={`flex flex-col h-screen w-64 shadow-lg p-4`}
      style={{ backgroundColor: theme.accentColor }}
    >
      {/* User Profile Section */}
      <div className="flex flex-col items-center mb-8">
        <div className="w-12 h-12 mb-2">
          <img
            src={profileImageSrc || "https://placehold.co/48x48"}
            alt={`${userName}'s Profile`}
            className="w-full h-full rounded-full  object-cover"
          />
        </div>
        <h2
          className="text-lg font-semibold"
          style={{ color: theme.textColor }}
        >
          {userName}
        </h2>
        <p className="text-sm" style={{ color: theme.textColor, opacity: 0.5 }}>
          {userId}
        </p>
      </div>

      {/* Navigation Buttons */}
      <div className="flex flex-col space-y-2 mb-auto" onClick={() => setRoomID("")}>
        <ThemedButton
          icon={<FaCalendarAlt className="mr-2" />}
          label="Events"
        />
        <ThemedButton icon={<FaHeadset className="mr-2" />} label="Support" />
      </div>

      {/* Footer Section */}
      <div className="flex flex-col space-y-2">
        <ThemedButton icon={<FaCog className="mr-2" />} label="Settings" />
        <ThemedButton icon={<FaSignOutAlt className="mr-2" />} label="Logout" />
      </div>
    </div>
  );
};

export default SideBar;
