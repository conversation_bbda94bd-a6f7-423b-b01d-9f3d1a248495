import { useTheme } from "context/themeContext";
import React, { Dispatch, SetStateAction } from "react";
import { FaPaperPlane, FaTimes } from "react-icons/fa";

type Props = {
  files: any[];
  message: string;
  setMessage: Dispatch<SetStateAction<string>>;
  sendMessage: () => void;
  placeHolder: string;
  sendButtonText: string;
  onRemoveFile?: (index: number) => void;
  maxHeight?: string;
};

const getMimeType = (fileName: string) => {
  const ext = fileName.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "pdf":
      return "application/pdf";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "png":
      return "image/png";
    case "gif":
      return "image/gif";
    case "txt":
      return "text/plain";
    case "csv":
      return "text/csv";
    default:
      return ""; // unsupported
  }
};

const getFileIcon = (mimeType: string) => {
  if (mimeType.startsWith("image/")) return "🖼️";
  if (mimeType === "application/pdf") return "📄";
  if (mimeType.startsWith("text/")) return "📝";
  return "📁";
};

const MultiFileViewer: React.FC<Props> = ({
  files,
  message,
  sendMessage,
  setMessage,
  placeHolder,
  sendButtonText,
  onRemoveFile,
  maxHeight = "80vh",
}) => {
  const { theme } = useTheme();

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) =>
        e.target === e.currentTarget && onRemoveFile && onRemoveFile(-1)
      }
    >
      <div
        className="rounded-lg shadow-xl overflow-hidden flex flex-col"
        style={{
          width: "100%",
          maxWidth: "900px",
          background: theme.bgColor,
          maxHeight: maxHeight,
        }}
      >
        <div
          className="p-4 border-b"
          style={{ borderColor: theme.accentColor }}
        >
          <h3
            className="text-lg font-semibold"
            style={{ color: theme.textColor }}
          >
            {files.length} File{files.length !== 1 ? "s" : ""} to Review
          </h3>
        </div>

        <div
          className="flex-1 overflow-y-auto p-4 grid gap-4"
          style={{
            gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
          }}
        >
          {files.map((file, index) => {
            const mimeType = getMimeType(file.name);
            const fileIcon = getFileIcon(mimeType);

            if (!mimeType) {
              return (
                <div
                  key={index}
                  className="border rounded-lg p-3 flex flex-col items-center justify-center text-center"
                  style={{
                    borderColor: theme.accentColor,
                    color: theme.textColor,
                  }}
                >
                  <span className="text-2xl mb-2">❌</span>
                  <p className="text-sm break-all">
                    Unsupported file type: {file.name}
                  </p>
                </div>
              );
            }

            return (
              <div
                key={index}
                className="border rounded-lg overflow-hidden flex flex-col relative group"
                style={{
                  borderColor: theme.accentColor,
                  minHeight: "200px",
                }}
              >
                {onRemoveFile && (
                  <button
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                    onClick={() => onRemoveFile(index)}
                    aria-label="Remove file"
                  >
                    <FaTimes size={12} />
                  </button>
                )}

                <div className="p-2 bg-gray-100 dark:bg-gray-800 flex items-center">
                  <span className="mr-2">{fileIcon}</span>
                  <p
                    className="text-xs font-medium truncate flex-1"
                    style={{ color: theme.secondaryColor }}
                    title={file.name}
                  >
                    {file.name}
                  </p>
                </div>

                <div className="flex-1 flex items-center justify-center overflow-hidden relative">
                  {mimeType.startsWith("image/") ? (
                    <img
                      src={file.base64}
                      alt={file.name}
                      className="w-full h-full object-contain"
                    />
                  ) : mimeType === "application/pdf" ? (
                    <iframe
                      src={file.base64}
                      title={file.name}
                      className="w-full h-full"
                      frameBorder="0"
                    />
                  ) : mimeType.startsWith("text/") ? (
                    <div className="p-2 w-full h-full overflow-auto text-xs whitespace-pre-wrap">
                      {atob(file.base64.split(",")[1])}
                    </div>
                  ) : (
                    <div className="p-4 text-center">
                      <span className="text-4xl">{fileIcon}</span>
                      <p className="text-sm mt-2">Preview not available</p>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div
          className="border-t p-3"
          style={{ borderColor: theme.accentColor }}
        >
          <div className="flex items-center rounded-lg px-3 py-2 relative">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={placeHolder}
              className="flex-1 bg-transparent outline-none"
              style={{ color: theme.textColor }}
              onKeyPress={handleKeyPress}
            />
            <button
              className="flex items-center justify-center rounded px-4 py-2 ml-2 transition-colors disabled:opacity-50"
              style={{
                backgroundColor: theme.accentColor,
                color: theme.secondaryColor || "white",
              }}
              onClick={sendMessage}
              disabled={!message.trim() && files.length === 0}
            >
              {sendButtonText}
              <FaPaperPlane className="ml-2" size={14} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiFileViewer;
