import { useTheme } from "context/themeContext";
import { MapPin } from "lucide-react";
import { Dispatch, SetStateAction, ReactElement } from "react";

type EventTextProps = {
  eventText: string;
  date: string;
  title: string;
  attendeeCount: number;
  attendeeImages: string[];
  eventThumbnail: string;
  attendeesText: string;
  viewEventText: string;
  setRoomID: Dispatch<SetStateAction<string>>;
  roomId: string;
};

const EventCards = ({
  eventText,
  date,
  title,
  attendeeCount,
  attendeeImages,
  eventThumbnail,
  attendeesText,
  viewEventText,
  setRoomID,
  roomId
}: EventTextProps) => {

  const {theme} = useTheme()

  return (
    <div
      className="flex items-center justify-between gap-4 my-2 cursor-pointer rounded-2xl"
      onClick={() => setRoomID(roomId)}
    >
      <img
        src={
          eventThumbnail
        }
        alt=""
        className="w-[160px] h-[118px] rounded-2xl"
      />
      <div
        className="border shadow flex-1 rounded-2xl px-4 py-3"
        style={{
          borderColor: theme.borderColor,
          background: theme.accentColor,
        }}
      >
        <h4
          style={{ color: theme.textColor }}
          className="font-semibold text-sm"
        >
          {date}
        </h4>
        <h2
          style={{ fontSize: "18px", color: theme.textColor }}
          className="font-bold "
        >
          {title}
        </h2>
        <div className="flex items-center" style={{ color: theme.textColor }}>
          <MapPin className="h-4" color={theme.textColor} />
          <p style={{ fontSize: "14px" }} className="">
            {eventText}
          </p>
        </div>
        <div className="flex items-center justify-between">
          <p
            style={{ fontSize: "14px", color: theme.secondaryColor }}
            className="underline font-normal"
          >
            {viewEventText}
          </p>
          <div className="flex items-center justify-between gap-2">
            <StackedImages images={attendeeImages} />
            <h3
              style={{ fontSize: "14px", color: theme.textColor }}
              className="font-normal flex items-center ml-5"
            >
              <span className="">{attendeeCount}</span>
              <span className="">{attendeesText}</span>
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventCards;

const StackedImages = ({ images }: { images: string[] }): ReactElement => {
  return (
    <div className="flex relative items-center">
      {images.map((image, index) => (
        <div
          key={index}
          className={`relative  transition-transform duration-300 ${
            index === 0 ? "z-10" : `-ml-12 z-${10 - index}`
          }`}
        >
          <img
            src={image}
            alt={` ${index + 1}`}
            className=" object-cover shadow-lg rounded-full h-[28px] w-[28px]"
          />
        </div>
      ))}
    </div>
  );
};
