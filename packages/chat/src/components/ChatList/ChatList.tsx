import { Dispatch, SetStateAction } from "react";
import EventCards from "./EventCards";
import { useTheme } from "context/themeContext";
import moment from "moment";
import { IRoom } from "@sparkstrand/chat-api-client/types";

type ChatListProps = {
  title: string;
  length: number;
  eventText: string;
  setRoomID: Dispatch<SetStateAction<string>>;
  rooms: IRoom [];
};

const ChatList = ({
  title,
  length,
  setRoomID,
  rooms,
}: ChatListProps) => {
  const { theme } = useTheme();
  return (
    <div className="w-full">
      <h3
        style={{ fontSize: "14px", color: theme.textColor }}
        className="flex font-medium border-b pb-2 border-[#D9D9D9]"
      >
        <span className="pr-2">{title}</span>(<span className="">{length}</span>
        )
      </h3>

      <div className="py-3">
        {rooms.map((room: IRoom) => (
          <EventCards
            attendeeCount={room.membersCount as number}
            attendeeImages={[]}
            date={moment(
              room?.metaData
                ? room?.metaData?.startDate
                : Date.now()
            ).format("MMMM Do, YYYY")}
            eventThumbnail={`${room?.avatar}`}
            eventText={"Event Location"}
            title={room?.name}
            attendeesText={"attendees"}
            viewEventText={"View Event Room"}
            key={room?.id}
            setRoomID={setRoomID}
            roomId={room?.id}
          />
        ))}
      </div>
    </div>
  );
};

export default ChatList;
