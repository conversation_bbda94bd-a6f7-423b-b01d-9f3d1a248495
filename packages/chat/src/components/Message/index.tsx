"use client";

import React, { useEffect, useState } from "react";
import { useFileUpload } from "@sparkstrand/chat-api-client/context";
import DocumentSliderModal from "components/ChatSideBar/ImageModal/DocumentSliderModal";
import ImageSliderModal from "components/ChatSideBar/ImageModal/ImageSliderModal";
import { useTheme } from "context/themeContext";
import useIntersectionObserver from "hooks/useIntersectionObserver";
import { IRoomMedia } from "@sparkstrand/chat-api-client/types";
import { getFileIcon } from "utils/getFilIcon";


interface MessageProps {
  isYou?: boolean;
  pic: string;
  message: string;
  time: string;
  user: string;
  files: IRoomMedia[];
  compact?: boolean;
}

const Message: React.FC<MessageProps> = ({
  isYou,
  message,
  pic,
  time,
  user,
  files,
  compact = false,
}) => {
  const { theme } = useTheme();
  const { getSignedUrl } = useFileUpload();
  const { elementRef, isIntersecting } = useIntersectionObserver({
    threshold: 0.25,
    rootMargin: "0px",
  });

  const [newFiles, setNewFiles] = useState<IRoomMedia[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDocModalOpen, setIsDocModalOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const formatTime = (isoString: string): string => {
    const date = new Date(isoString);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
      timeZone: "UTC",
    });
  };

  const stringToColor = (str: string): string => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    return `hsl(${hash % 360}, 70%, 50%)`;
  };

  const imageCount = Math.min(files.length, 4);
  const gridCols =
    imageCount === 1
      ? "grid-cols-1"
      : imageCount === 2
        ? "grid-cols-2"
        : imageCount === 3
          ? "grid-cols-3"
          : "grid-cols-2";
  const gridRows = imageCount === 3 ? "grid-rows-1" : "grid-rows-2";

  const getAllUrl = async () => {
    const existingIds = new Set(newFiles.map((file) => file.id));
    const filesToFetch = files.filter((file) => !existingIds.has(file.id));
    if (filesToFetch.length === 0) return;

    const urls = await Promise.all(
      filesToFetch.map(async (file) => {
        const url = await getSignedUrl(file.id, {
          baseUrl: process.env.NEXT_PUBLIC_CHAT_SERVER_URL,
          inline: true,
        });
        return { ...file, fileUrl: url };
      })
    );
    setNewFiles((prev) => [...prev, ...urls]);
  };

  useEffect(() => {
    if (isIntersecting && files.length > 0) {
      getAllUrl();
    }
  }, [isIntersecting]);

  return (
    <div className={`flex flex-col gap-3 mb-5 font-sans`}>
      {newFiles.length > 0 && (
        <>
          <ImageSliderModal
            images={newFiles}
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            selectedIndex={selectedIndex}
            setSelectedIndex={setSelectedIndex}
          />
          <DocumentSliderModal
            documents={newFiles}
            isOpen={isDocModalOpen}
            onClose={() => setIsDocModalOpen(false)}
            selectedIndex={selectedIndex}
            setSelectedIndex={setSelectedIndex}
          />
        </>
      )}

      <div
        ref={elementRef}
        className={`flex gap-3 items-end ${isYou ? "justify-end" : ""} `}
      >
        {/* Profile */}
        <div
          className={`${
            isYou ? "order-2" : "order-1"
          } w-[40px] h-[40px] rounded-full flex-shrink-0`}
        >
          {pic ? (
            <img
              width={40}
              height={40}
              src={pic}
              alt="profile"
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-300 flex items-center justify-center font-bold rounded-full">
              {user.charAt(0)}
            </div>
          )}
        </div>

        {/* Bubble */}
        <div
          className={`${
            isYou ? "order-1" : "order-2"
          } w-fit ${compact ? "md:max-w-[75%]" : "md:max-w-[60%]"} min-w-[100px] p-2 px-3 rounded-lg shadow-md ${
            isYou ? "rounded-tr-none text-white" : "rounded-tl-none"
          }`}
          style={{
            background: isYou ? theme.secondaryColor : theme.accentColor,
            color: theme.textColor,
          }}
        >
          <h4
            className={`${compact ? "text-xs" : "text-sm"} font-bold mb-1`}
            style={{ color: isYou ? "#fff" : stringToColor(user) }}
          >
            {user}
          </h4>
          <p className={compact ? "text-xs" : "text-sm"}>{message}</p>
          <br />
          {files.length > 0 && (
            <div
              className={`grid gap-2 ${gridCols} ${
                imageCount === 4 ? gridRows : ""
              } w-full`}
            >
              {files.slice(0, 4).map((file, index) => {
                const matchedUrl =
                  newFiles.find((f) => f.id === file.id)?.fileUrl || "";
                const isImage = file.fileType.toLowerCase().includes("image");

                return (
                  <div
                    key={index}
                    className="border rounded shadow relative overflow-hidden w-full h-full"
                    onClick={() => setSelectedIndex(index)}
                  >
                    {isImage ? (
                      <img
                        src={matchedUrl}
                        alt={file.filename}
                        className={`w-full object-cover ${
                          compact ? "h-[140px]" : "h-[200px]"
                        }`}
                        onClick={() => setIsModalOpen(true)}
                      />
                    ) : (
                      <div onClick={() => setIsDocModalOpen(true)}>
                        <div
                          className="min-w-[150px] p-4 rounded-md cursor-pointer flex flex-col items-center transition-transform text-center"
                          style={{ background: theme.secondaryColor }}
                        >
                          <span className="text-4xl">
                            {getFileIcon(file.fileType)}
                          </span>
                          <span
                            className="mt-2 font-bold text-xs break-words"
                            style={{ color: theme.textColor }}
                          >
                            {file.filename}
                          </span>
                          <span
                            className="mt-1 text-xs"
                            style={{ color: theme.textColor }}
                          >
                            {file.fileType.toUpperCase()}
                          </span>
                        </div>
                      </div>
                    )}
                    {index === 3 && files.length > 4 && (
                      <div
                        className="absolute top-0 left-0 w-full h-full flex items-center justify-center text-white text-2xl font-bold backdrop-blur"
                        style={{ color: theme.textColor }}
                        onClick={() => setIsModalOpen(true)}
                      >
                        +{files.length - 4}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Time */}
      <div
        className={`flex ${isYou ? "justify-end" : ""} ${compact ? "px-2" : "px-[70px]"}`}
      >
        <p className="text-xs" style={{ color: theme.textColor }}>
          {formatTime(time)}
        </p>
      </div>
    </div>
  );
};

export default Message;
