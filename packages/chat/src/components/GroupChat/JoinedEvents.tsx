import { useTheme } from "context/themeContext";
import { Dispatch, SetStateAction, useEffect, useState, useMemo } from "react";
import { IoSearch } from "react-icons/io5";
import { useChatRoom } from "@sparkstrand/chat-api-client/context";
import moment from "moment";
import CustomPagination from "./CustomPagination";

interface JoinedEventsProps {
  setRoomID: Dispatch<SetStateAction<string>>;
  fallbackImage?: string;
}

const DEFAULT_FALLBACK_IMAGE =
  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='%23ddd'%3E%3Crect width='100' height='100'/%3E%3C/svg%3E";

const JoinedEvents: React.FC<JoinedEventsProps> = ({
  setRoomID,
  fallbackImage = DEFAULT_FALLBACK_IMAGE,
}) => {
  const { theme } = useTheme() || {
    theme: {
      textColor: "#ffffff",
      secondaryColor: "#2c7c51",
      accentColor: "rgba(255, 255, 255, 0.1)",
    },
  };

  const { rooms = [], emitGetListOfGuestRooms } = useChatRoom() || {};
  const roomsPerPage = 3;
  const [currentPage, setCurrentPage] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined" && emitGetListOfGuestRooms) {
      emitGetListOfGuestRooms();
    }
  }, []);

  const filteredRooms = useMemo(() => {
    try {
      const query = searchQuery.toLowerCase();
      return rooms.reverse().filter(
        (room) =>
          room?.name?.toLowerCase().includes(query) ||
          room?.description?.toLowerCase().includes(query)
      );
    } catch (error) {
      console.error("Error filtering rooms:", error);
      return [];
    }
  }, [rooms, searchQuery]);

  const { paginatedRooms, totalPages } = useMemo(() => {
    const startIndex = currentPage * roomsPerPage;
    return {
      paginatedRooms: filteredRooms.slice(
        startIndex,
        startIndex + roomsPerPage
      ),
      totalPages: Math.ceil(filteredRooms.length / roomsPerPage),
    };
  }, [filteredRooms, currentPage]);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = fallbackImage;
  };

  const getSafeImageUrl = (url: string | undefined) => {
    return url || fallbackImage;
  };

  return (
    <div className="w-full p-4 md:p-6 min-h-screen">
      <div className="w-full flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h2
          className="text-2xl font-normal font-antonio"
          style={{ color: theme?.textColor }}
        >
          Joined Groups
        </h2>
        <div
          className="w-full md:w-[269px] h-[42px] border flex items-center px-4 gap-3 rounded-xl"
          style={{ borderColor: theme?.accentColor }}
        >
          <IoSearch color={theme?.textColor} size={20} />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setCurrentPage(0);
            }}
            placeholder="Search groups..."
            className="flex-1 bg-transparent outline-none border-none"
            style={{ color: theme?.textColor }}
          />
        </div>
      </div>

      <div className="w-full max-w-6xl mx-auto">
        <div className="grid grid-cols-1 gap-4">
          {paginatedRooms.length > 0 ? (
            paginatedRooms.map((room) => (
              <div
                key={room?.id}
                onClick={() => room?.id && setRoomID(room.id)}
                className="border rounded-xl p-4 cursor-pointer transition-all duration-300 hover:shadow-lg relative min-h-[200px] flex flex-col"
                style={{
                  borderColor: `${theme?.accentColor}30`,
                  backgroundColor:
                    theme?.accentColor || "rgba(255, 255, 255, 0.05)",
                }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full border overflow-hidden flex-shrink-0">
                      <img
                        src={getSafeImageUrl(room?.avatar?.fileUrl)}
                        alt="Group Avatar"
                        className="w-full h-full object-cover"
                        onError={handleImageError}
                      />
                    </div>
                    <div>
                      <h3
                        className="font-semibold line-clamp-1"
                        style={{ color: theme?.textColor }}
                      >
                        {room?.name || "Unnamed Group"}
                      </h3>
                      <p
                        className="text-xs opacity-60"
                        style={{ color: theme?.textColor }}
                      >
                        Created:{" "}
                        {room?.createdAt
                          ? moment(room.createdAt).format("MMM D, YYYY")
                          : "Unknown date"}
                      </p>
                    </div>
                  </div>
                  <div
                    className="px-2 py-1 rounded text-xs font-bold"
                    style={{
                      backgroundColor: theme?.secondaryColor,
                      color: theme?.textColor,
                    }}
                  >
                    {room?.membersCount || 0} Member
                    {room?.membersCount !== 1 ? "s" : ""}
                  </div>
                </div>

                <div className="flex-1 flex flex-col">
                  <div className="flex items-center gap-1 mb-2">
                    <span
                      className="text-xs font-semibold"
                      style={{ color: theme?.textColor }}
                    >
                      Status:
                    </span>
                    <span
                      className="text-xs capitalize"
                      style={{ color: theme?.secondaryColor }}
                    >
                      {room?.type || "unknown"}
                    </span>
                  </div>
                  <p
                    className="text-sm line-clamp-3"
                    style={{ color: theme?.textColor }}
                  >
                    {room?.description || "No description available"}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div
              className="col-span-full py-12 text-center"
              style={{ color: theme?.textColor }}
            >
              {searchQuery ? "No matching groups found" : "No groups available"}
            </div>
          )}
        </div>

        <CustomPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </div>
  );
};

export default JoinedEvents;
