"use client";

import { Dispatch, SetStateAction, useEffect, useState, useMemo } from "react";
import { IoSearch } from "react-icons/io5";
import { LuMapPin } from "react-icons/lu";
// import ReactPaginate from "react-paginate";
import { IRoom } from "@sparkstrand/chat-api-client/types";
import { useChatRoom } from "@sparkstrand/chat-api-client/context";
import { useTheme } from "context/themeContext";
import moment from "moment";
import CustomPagination from "./CustomPagination";

interface GroupChatProps {
  setRoomID: Dispatch<SetStateAction<string>>;
  fallbackImage?: string;
}

const DEFAULT_FALLBACK_IMAGE =
  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='%23ddd'%3E%3Crect width='100' height='100'/%3E%3C/svg%3E";

const GroupChat: React.FC<GroupChatProps> = ({
  setRoomID,
  fallbackImage = DEFAULT_FALLBACK_IMAGE,
}) => {
  const [pickerType, setPickerType] = useState<"Past" | "Upcoming">("Past");
  const { theme } = useTheme() || {
    theme: { textColor: "#ffffff", secondaryColor: "#2c7c51" },
  };
  const { rooms = [], emitGetListOfGuestRooms } = useChatRoom() || {};
  const roomsPerPage = 3;
  const [currentPage, setCurrentPage] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined" && emitGetListOfGuestRooms) {
      emitGetListOfGuestRooms();
    }
  }, []);

  const { filteredRooms, pastLength, upcomingLength } = useMemo(() => {
    const currentTime = Date.now();
    const filtered = rooms.reverse().filter((room) =>
      room?.name?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const past = filtered.filter(
      (room) =>
        room?.metaData?.startDate &&
        new Date(room.metaData.startDate).getTime() < currentTime
    );

    const upcoming = filtered.filter(
      (room) =>
        room?.metaData?.startDate &&
        new Date(room.metaData.startDate).getTime() > currentTime
    );

    return {
      filteredRooms: pickerType === "Past" ? past : upcoming,
      pastLength: past.length,
      upcomingLength: upcoming.length,
    };
  }, [rooms, searchQuery, pickerType]);

  const totalPages = Math.max(
    1,
    Math.ceil(filteredRooms.length / roomsPerPage)
  );
  const roomsToShow = useMemo(() => {
    return filteredRooms.slice(
      currentPage * roomsPerPage,
      (currentPage + 1) * roomsPerPage
    );
  }, [filteredRooms, currentPage]);

  

  const getSafeImageUrl = (url: string | undefined) => {
    return url || fallbackImage;
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = fallbackImage;
  };
  

  return (
    <div className="w-full p-6 min-h-screen">
      <div className="w-full flex-col md:flex-row flex items-start justify-between md:items-center">
        <h2
          className="text-2xl font-normal font-antonio"
          style={{ color: theme?.textColor }}
        >
          Groups
        </h2>
        <div className="w-[269px] h-[42px] border border-white/15 flex items-center px-4 gap-3 rounded-xl">
          <IoSearch color={theme?.textColor} size={20} />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1 bg-transparent outline-none border-none text-white"
            placeholder="Search groups..."
          />
        </div>
      </div>

      <div className="w-full md:w-4/5 mx-auto flex flex-col items-center mt-8">
        <div className="w-full md:w-[70%] h-[45px] bg-gray-100 rounded-xl flex p-1 mb-8">
          {["Past", "Upcoming"].map((type) => (
            <button
              key={type}
              type="button"
              onClick={() => {
                setPickerType(type as "Past" | "Upcoming");
                setCurrentPage(0);
              }}
              className={`w-1/2 h-full flex justify-center items-center rounded-xl cursor-pointer transition-all duration-300 ${
                pickerType === type ? "bg-green-700 text-white" : "text-black"
              }`}
            >
              {type} Events
            </button>
          ))}
        </div>

        <div className="w-full border-b border-gray-300 text-white text-sm font-medium pb-1 mb-6">
          {pickerType} Events (
          {pickerType === "Past" ? pastLength : upcomingLength})
        </div>

        {roomsToShow.length === 0 ? (
          <div className="w-full text-center text-white py-10">
            No {pickerType.toLowerCase()} events found
          </div>
        ) : (
          <div className="w-full flex flex-col gap-3">
            {roomsToShow.map((room: IRoom, index: number) => {
              const guests = [
                ...(room?.guests?.admins || []),
                ...(room?.guests?.members || []),
                ...(room?.guests?.moderators || []),
              ];

              const startDate = room?.metaData?.startDate
                ? moment(room.metaData.startDate).format("MMMM Do, YYYY")
                : "Date not specified";

              return (
                <div
                  key={room?.id || index}
                  onClick={() => room?.id && setRoomID(room.id)}
                  className="list-container w-full h-fit p-1.5 rounded-xl flex gap-2 md:gap-8 items-center cursor-pointer transition-all duration-300 border border-transparent hover:border-green-600"
                  style={
                    {
                      "--hover-border-color": theme?.secondaryColor,
                    } as React.CSSProperties
                  }
                >
                  <div className="w-2/6 md:w-[160px] h-[118px] rounded-xl overflow-hidden border border-white">
                    <img
                      src={getSafeImageUrl(room?.avatar?.fileUrl)}
                      alt="Group chat"
                      className="w-full h-full object-cover"
                      onError={handleImageError}
                    />
                  </div>
                  <div className="flex-1 h-full bg-white/5 border border-white/10 rounded-xl p-1.5 md:p-3.5 flex flex-col justify-between">
                    <div className="flex flex-col gap-1.5">
                      <h4 className="text-white text-[10px] md:text-sm font-semibold">
                        {startDate}
                      </h4>
                      <h3 className="text-white text-xs md:text-lg font-bold">
                        {room?.name || "Untitled Group"}
                      </h3>
                      {room?.metaData?.location?.name && (
                        <div className="flex items-center gap-1.5 md:items-end">
                          <LuMapPin color="#fff" size={16} />
                          <p className="text-white text-[10px] md:text-sm font-semibold">
                            {room.metaData.location.name}
                          </p>
                        </div>
                      )}
                    </div>
                    <div className="flex mt-1 md:mt-[unset] flex-col md:flex-row gap-2 md:gap-[unset] justify-between item-start md:items-center">
                      <button className="w-fit order-2 md:order-1 text-white text-[10px] md:text-sm border-b border-white">
                        View Event Room
                      </button>
                      <div className="flex w-fit order-1 md:order-2 md:flex-1 justify-end items-center gap-7">
                        <div className="flex -space-x-2">
                          {guests.slice(0, 5).map(({ avatar }, i) => (
                            <div
                              key={i}
                              className="w-5 h-5 md:w-7 md:h-7 bg-white rounded-full overflow-hidden p-[1px] border border-white"
                            >
                              <img
                                src={getSafeImageUrl(avatar?.fileUrl)}
                                alt="Attendee"
                                className="w-full h-full object-cover rounded-full"
                                onError={handleImageError}
                              />
                            </div>
                          ))}
                        </div>
                        <p className="text-white text-[10px] md:text-sm transform -translate-x-5">
                          {room?.membersCount || 0} attendee
                          {(room?.membersCount || 0) !== 1 && "s"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        <CustomPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </div>
  );
};

export default GroupChat;
