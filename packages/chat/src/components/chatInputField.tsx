"use client";

import { FC, useState, ChangeEvent } from "react";
import {
  FaMicrophone,
  FaPaperclip,
  FaRegSmile,
  FaPaperPlane,
} from "react-icons/fa";
import EmojiPicker from "emoji-picker-react";
import { EmojiClickData } from "emoji-picker-react";
import { useTheme } from "context/themeContext";
import {
  useChat,
  useUserTyping,
  useFileUpload,
} from "@sparkstrand/chat-api-client/context";
import MultiFileViewer from "./MultiFileViewer";
import { FilePreview, ISendMessage } from "@sparkstrand/chat-api-client/types";

interface ChatInputFieldProps {
  sendButtonText?: string;
  placeholderText?: string;
  emojiButtonText?: string;
  pauseButtonText?: string;
  resumeButtonText?: string;
  sendMessage: (message: ISendMessage) => void;
  roomId: string;
}

const ChatInputField: FC<ChatInputFieldProps> = ({
  sendButtonText = "Send",
  placeholderText = "Type Message...",
  pauseButtonText = "Pause",
  resumeButtonText = "Resume",
  sendMessage,
  roomId,
}) => {
  const [message, setMessage] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [recording, setRecording] = useState(false);
  const { sendTypingIndicator, sendStopTypingIndicator } = useUserTyping();
  const [audioURL, setAudioURL] = useState("");
  const { userId } = useChat();
  // Using underscore prefix to indicate intentionally unused variable
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_isPaused, setIsPaused] = useState(false);
  const { theme } = useTheme();
  const { addFiles, selectedFiles, clearFiles } = useFileUpload();

  const handleEmojiClick = (emojiObject: EmojiClickData): void => {
    setMessage((prev) => prev + emojiObject.emoji);
    setShowEmojiPicker(false);
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>): void => {
    addFiles(event?.target?.files as FileList);
  };

  const handleSendMessage = (): void => {
    if (message.trim()) {
      if (message.trim() && userId) {
        const messageToSend = {
          to: roomId,
          text: message,
          senderId: userId,
          files: selectedFiles.map((f: FilePreview) => f.file),
        };
        sendMessage(messageToSend);
        setMessage("");
        clearFiles();
      }
    }
    if (audioURL) {
      setAudioURL("");
    }
  };

  const startRecording = (): void => {
    setRecording(true);
    setIsPaused(false);
  };

  const stopRecording = (): void => {
    setRecording(false);
    setIsPaused(false);
  };

  const pauseRecording = (): void => {
    setRecording(false);
    setIsPaused(true);
  };

  const resumeRecording = (): void => {
    setRecording(true);
    setIsPaused(false);
  };

  // const onStop = (recordedData: {
  //   blob: Blob;
  //   startTime: number;
  //   stopTime: number;
  //   blobURL: string;
  // }): void => {
  //   setAudioURL(URL.createObjectURL(recordedData.blob));
  // };

  return (
    <>
      {selectedFiles.length > 0 && (
        <MultiFileViewer
          files={selectedFiles.map((f: FilePreview) => {
            return {
              name: f.file.name,
              base64: f.preview,
            };
          })}
          message={message}
          placeHolder="Enter Text"
          sendMessage={handleSendMessage}
          setMessage={setMessage}
          sendButtonText={sendButtonText}
        />
      )}
      <div
        className="w-full flex items-center rounded-lg p-2 mx-1 md:mx-2 relative"
        style={{ background: theme.accentColor }}
      >
        {message.length < 1 && (
          <div className="relative">
            <FaMicrophone
              className={` mr-2 cursor-pointer`}
              onMouseDown={startRecording}
              onMouseUp={stopRecording}
              style={{
                color: `${recording ? "text-red-500" : theme.textColor}`,
              }}
            />
            {/* <ReactMic
            ref={micRef}
            record={recording}
            className="absolute top-[-100px]"
            onStop={(blobObject) =>
              onStop({
                ...blobObject,
                blobURL: URL.createObjectURL(blobObject.blob),
              })
            }
            strokeColor={theme.textColor}
            backgroundColor="transparent"
          /> */}
          </div>
        )}
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder={placeholderText}
          className="flex-1 bg-transparent placeholder-gray-400 outline-none"
          style={{ color: theme.textColor }}
          onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
          onFocus={() => sendTypingIndicator(roomId)}
          onBlur={() => sendStopTypingIndicator(roomId)}
        />
        <div className="flex items-center space-x-1 md:space-x-2">
          <FaRegSmile
            className="cursor-pointer"
            style={{ color: theme.textColor }}
            onClick={() => setShowEmojiPicker((prev) => !prev)}
          />
          <input
            type="file"
            className="hidden"
            id="file-input"
            onChange={handleFileChange}
            multiple
          />
          <label htmlFor="file-input">
            <FaPaperclip
              style={{ color: theme.textColor }}
              className="cursor-pointer"
            />
          </label>
          <button
            className=" w-fit flex items-center justify-center gap-1 bg-gray-700  rounded px-1 md:px-3 py-1"
            onClick={handleSendMessage}
            style={{ color: theme.textColor }}
          >
            <p className="text-white text-sm md:flex">
              {sendButtonText}
            </p>

            <FaPaperPlane className="" />
          </button>
          {recording && (
            <div className="flex space-x-2">
              <button
                style={{ color: theme.textColor }}
                onClick={pauseRecording}
              >
                {pauseButtonText}
              </button>
              <button
                style={{ color: theme.textColor }}
                onClick={resumeRecording}
              >
                {resumeButtonText}
              </button>
            </div>
          )}
        </div>
        {showEmojiPicker && (
          <div className="absolute z-10 bottom-3">
            <EmojiPicker
              onEmojiClick={(emojiData) => handleEmojiClick(emojiData)}
              style={{ background: theme.accentColor }}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default ChatInputField;
