"use client";
import { useTheme } from "context/themeContext";
import React, { Dispatch, SetStateAction } from "react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  previousText?: string;
  nextText?: string;
  setCurrentPage: Dispatch<SetStateAction<number>>;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  previousText = "Previous",
  nextText = "Next",
  setCurrentPage
}) => {
  const {theme} = useTheme()
  
  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = (): void => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const renderPageNumbers = (): JSX.Element[] => {
    const pages = [];
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1, 2, 3);
      if (currentPage > 4) {
        pages.push("...");
      }
      for (
        let i = Math.max(4, currentPage - 1);
        i <= Math.min(totalPages - 3, currentPage + 1);
        i++
      ) {
        pages.push(i);
      }
      if (currentPage < totalPages - 3) {
        pages.push("...");
      }
      pages.push(totalPages - 2, totalPages - 1, totalPages);
    }
    return pages.map((page, index) => (
      <button
        key={index}
        onClick={() => typeof page === "number" && setCurrentPage(page)}
        className={`w-10 h-10 flex items-center cursor-pointer  justify-center rounded-lg font-bold `}
        style={{
          background: `${currentPage === page ? theme.secondaryColor : "transparent"}`,
          color: theme.textColor,
        }}
        disabled={typeof page !== "number"}
      >
        {page}
      </button>
    ));
  };

  return (
    <div className="w-full flex items-center justify-between px-6">
      <button
        onClick={handlePrevious}
        className="px-4 py-2 border rounded-lg cursor-pointer disabled:opacity-50"
        aria-label="Previous Page"
        style={{ borderColor: theme.activeChatBorder, color: theme.textColor }}
        disabled={currentPage === 1}
      >
        {previousText}
      </button>
      <div className="flex space-x-2">{renderPageNumbers()}</div>
      <button
        onClick={handleNext}
        className="px-4 py-2 border rounded-lg cursor-pointer disabled:opacity-50"
        style={{ borderColor: theme.activeChatBorder, color: theme.textColor }}
        aria-label="Next Page"
        disabled={currentPage === totalPages}
      >
        {nextText}
      </button>
    </div>
  );
};

export default Pagination;
