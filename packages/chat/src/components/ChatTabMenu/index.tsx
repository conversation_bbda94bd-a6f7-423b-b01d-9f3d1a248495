import { useTheme } from "context/themeContext";
import  {Disp<PERSON>, <PERSON>, SetStateAction } from "react";

interface ChatTabMenuProps {
  pastLabel?: string;
  upcomingLabel?: string;
  joinedLabel?: string;
  setEventsToShow: Dispatch<SetStateAction<string>>;
  eventsToShow: string;
}

const ChatTabMenu: FC<ChatTabMenuProps> = ({
  pastLabel = "Past Events",
  upcomingLabel = "Upcoming Events",
  joinedLabel = "Joined Events",
  eventsToShow,
  setEventsToShow
}) => {
  const {theme} = useTheme()

  return (
    <div
      className="flex justify-between rounded-lg w-full"
      style={{ background: theme.accentColor }}
    >
      <button
        className={`cursor-pointer flex-1  px-4 py-2 rounded-lg transition-colors duration-300 font-medium `}
        onClick={() => setEventsToShow("past")}
        style={{
          background:
            eventsToShow === "past" ? theme.secondaryColor : "transparent",
            color: theme.textColor
        }}
      >
        {pastLabel}
      </button>
      <button
        className={`cursor-pointer flex-1 px-4 py-3 rounded-lg transition-colors duration-300 font-medium `}
        onClick={() => setEventsToShow("upcoming")}
        style={{
          background:
            eventsToShow === "upcoming" ? theme.secondaryColor : "transparent",
            color: theme.textColor
        }}
      >
        {upcomingLabel}
      </button>
      <button
        className={`cursor-pointer flex-1 px-4 py-3 rounded-lg transition-colors duration-300 font-medium `}
        onClick={() => setEventsToShow("joined")}
        style={{
          background:
            eventsToShow === "joined" ? theme.secondaryColor : "transparent",
            color: theme.textColor
        }}
      >
        {joinedLabel}
      </button>
    </div>
  );
};

export default ChatTabMenu;
