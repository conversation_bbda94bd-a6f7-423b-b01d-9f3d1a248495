"use client";

import { useChatRoom } from "@sparkstrand/chat-api-client/context";
import { useTheme } from "context/themeContext";
import { ArrowLeft } from "lucide-react";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { FaPlus, FaRegBell } from "react-icons/fa";

const fallbackImage =
"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='%23ddd'%3E%3Crect width='100' height='100'/%3E%3C/svg%3E";
interface TopBarProps {
  username?: string;
  buttonText?: string;
  hasNotification?: boolean;
  roomID: string;
  setShowSideBar: Dispatch<SetStateAction<boolean>>;

  onClose?: () => void
}

const TopBar: React.FC<TopBarProps> = ({
  username = "",
  hasNotification = true,
  roomID,
  setShowSideBar,
  onClose
}) => {
  const [showNotification, setShowNotification] = useState(hasNotification);
  const { theme } = useTheme();
  const { getRoomDataById, currentRoomData } = useChatRoom();

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = fallbackImage;
  };

  const handleNotificationClick = (): void => {
    setShowNotification(false); // Clear notification when clicked
  };

  useEffect(() => {
    if (roomID) {
      getRoomDataById(roomID);
    }
  }, [roomID]);

  return (
    <div
      className="flex gap-2 justify-between items-center w-full px-3 md:px-5 py-3 border-b border-[#D9D9D9]"
      style={{ backgroundColor: theme.accentColor }}
    >
      {/* Welcome message */}
      <div
        className="font-medium max-w-4/6 flex items-center gap-2 text-xs md:text:lg "
        style={{ color: theme.textColor }}
      >
        { (
          <>
            <ArrowLeft onClick={() => {
              onClose && onClose()
              }} size={32} color={theme.textColor} />
            <img
              src={currentRoomData?.avatar?.fileUrl || fallbackImage}
              alt="room avatar"
              className="w-[30px] h-[30px] object-cover rounded-lg border border-gray-100"
              onError={handleImageError}
            />
          </>
        )}
        {roomID ? currentRoomData?.name : `Welcome ${username}`}
        {!roomID && (
          <span role="img" aria-label="waving hand">
            👋
          </span>
        )}
      </div>

      {/* Right side actions */}
      <div className="flex items-center space-x-4 gap-2">
        {/* Create button */}
        {roomID && <button
          onClick={() => setShowSideBar(true)}
          className="flex items-center  border  rounded px-1 md:px-2 py-2  hover:bg-gray-800 transition-colors m-0"
          style={{ color: theme.textColor, borderColor: theme.borderColor }}
        >
          <FaPlus size={16} color={theme.textColor} />
          <span className="text-[10px] text-white md:text-sm" style={{ color: theme.textColor }}>
            View Room Info
          </span>
        </button>}

        {/* Notification icon */}
        <div
          className="relative cursor-pointer"
          onClick={handleNotificationClick}
        >
          <FaRegBell size={20} color={theme.textColor} />
          {showNotification && (
            <span className="absolute -top-1 -right-1 bg-red-500 rounded-full w-2 h-2"></span>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopBar;
