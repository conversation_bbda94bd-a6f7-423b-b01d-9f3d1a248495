import { useEffect, useRef, useState } from "react";

const useIntersectionObserver = ({
  threshold = 0.1,
  root = null,
  rootMargin = "0px",
} = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        root,
        rootMargin,
        threshold,
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      if (elementRef.current) {
        observer.unobserve(elementRef.current);
      }
    };
  }, [root, rootMargin, threshold]);

  return { elementRef, isIntersecting };
};

export default useIntersectionObserver;
