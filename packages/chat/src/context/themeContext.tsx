import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useMemo,
} from "react";

// Theme shape
export type Theme = {
  bgColor: string;
  hoverColor: string;
  textColor: string;
  borderColor: string;
  activeChatBorder: string;
  accentColor: string;
  secondaryColor: string;
  height: string;
};

type ThemeContextType = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  updateTheme: (partial: Partial<Theme>) => void;
};

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider component that accepts an initial theme from parent app
export const ThemeProvider = ({
  children,
  initialTheme,
}: {
  children: ReactNode;
  initialTheme: Theme;
}) => {
  const [theme, setTheme] = useState<Theme>(initialTheme);

  const updateTheme = (partial: Partial<Theme>) => {
    setTheme((prev) => ({ ...prev, ...partial }));
  };

  const value = useMemo(() => ({ theme, setTheme, updateTheme }), [theme]);

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

// Hook to consume the theme
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};
