import { 
    Message, 
    IMessage, 
    ICreateMessage, 
    ISearchFilters, 
    ISearchOptions, 
    ISearchResult,
    IMessageStats, 
    IMessageActivity,
    //IUpdateMessage, 
    //IRoomPermissionType 
} from "../../models";
import { IResponse } from "../../utils";
import { RoomService } from "./room.service";



export class MessageService {
    constructor(
        private readonly messageModel: Message, 
    ) {}

    async createMessage(data: ICreateMessage): Promise<IResponse<IMessage | null>> {
        try {
            const result = await this.messageModel.createMessage(data);
            return this.messageModel.formatResponse<IMessage>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message created successfully'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: error.message
                });
            }
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to create message'
            });
        }
    }

    async editMessageText(id: string, text: string, updatorId: string): Promise<IResponse<IMessage | null>> {
        try {
            const message = await this.messageModel.getMessageById(id);
            if(!message) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.notFoundParams,
                    message: 'Message not found'
                });
            }
            if(message.sender.id !== updatorId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.forbiddenParams,
                    message: 'Cannot edit another member\'s message.'
                });
            }
            const result = await this.messageModel.editMessage(id, text);
            return this.messageModel.formatResponse({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message edited successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to update message'
            });
        }
    }

    async deleteMessage(id: string, deleterId: string): Promise<IResponse<IMessage | null>> {
        try {
            const message = await this.messageModel.getMessageById(id);
            if(!message) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.notFoundParams,
                    message: 'Message not found'
                });
            }
            if(message.sender.id !== deleterId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.forbiddenParams,
                    message: 'Cannot delete another member\'s message.'
                });
            }
            await this.messageModel.deleteMessage(id);
            return this.messageModel.formatResponse({
                ...this.messageModel.successParams,
                data: message,
                message: 'Message deleted successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to delete message'
            });
        }
    }
    
    /**
     * Get last message in a room
     * @param roomId 
     * @returns 
     */
    async getLastMessageInRoom(roomId: string): Promise<IResponse<IMessage | null>> {
        try {
            const result = await this.messageModel.getLastMessageInRoom(roomId);
            return this.messageModel.formatResponse({
                ...(result ? this.messageModel.successParams : this.messageModel.notFoundParams), 
                data: result,
                message: result ? 'Last message found' : 'Last message not found'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: error.message
                });
            }
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get last message'
            });
        }
    }

    /**
     * Get all the messages that the guest has missed in the room
     * Return messages from oldest to newest
     * @param guestId 
     * @param roomId 
     * @returns 
     */
    async getGuestMissedMessages(guestId: string, roomId: string): Promise<IResponse<IMessage[]>> {
        try {
            const result = await this.messageModel.getGuestMissedMessages(guestId, roomId);
            return this.messageModel.formatResponse({
                ...(result.length > 0 ? this.messageModel.successParams : this.messageModel.notFoundParams), 
                data: result,
                message: result.length > 0 ? 'Missed messages found' : 'No missed messages found'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: error.message
                });
            }
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get missed messages'
            });
        }
    }

    /**
     * Get a Guest last Read Message Id and the read timestamp
     * @param guestId 
     * @param roomId 
     * @returns 
     */
    async getGuestLastReadMessage(guestId: string, roomId: string): Promise<IResponse<{ lastReadMessageId?: string, lastReadAt?: Date }>> {
        try {
            const result = await this.messageModel.getGuestLastReadMessage(guestId, roomId);
            return this.messageModel.formatResponse({
                ...(result ? this.messageModel.successParams : this.messageModel.notFoundParams), 
                data: result,
                message: result ? 'Last read message found' : 'Last read message not found'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: error.message
                });
            }
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get last read message'
            });
        }
    }

    async markMessageRead(ids: string[]): Promise<IResponse<boolean>> {
        try {
            const result = await this.messageModel.markMessageRead(ids);
            return this.messageModel.formatResponse({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message marked as read successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to mark message as read'
            });
        }
    }

    async createThread(parentId: string, data: ICreateMessage): Promise<IResponse<IMessage | null>> {
        try {
            if(!data.senderId || !parentId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Missing SenderId or parentId'
                });
            }
 
            const result = await this.messageModel.createThread(parentId, data);
            return this.messageModel.formatResponse<IMessage>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Thread created successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to create thread'
            });
        }
    }

    async getThread(id: string): Promise<IResponse<IMessage[]>> {
        try {
            const result = await this.messageModel.getThread(id);
            return this.messageModel.formatResponse<IMessage[]>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Thread retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<[]>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to retrieve thread'
            });
        }
    }

    // ============= SEARCH FUNCTIONALITY =============

    /**
     * Search messages in a specific room - will only be used by a company admins
     */
    async searchMessagesInRoom(
        roomId: string,
        words: string[],
        filters?: ISearchFilters,
        options?: ISearchOptions
    ): Promise<IResponse<ISearchResult>> {
        try {
            if (!roomId || !words || words.length === 0) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Room ID and search words are required'
                });
            }

            const result = await this.messageModel.searchMessagesInRoom(roomId, words, filters, options);
            return this.messageModel.formatResponse<ISearchResult>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Messages searched successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to search messages in room'
            });
        }
    }

    /**
     * Search messages across multiple rooms
     */
    async searchMessagesInRooms(
        words: string[],
        roomIds: string[],
        filters?: ISearchFilters,
        options?: ISearchOptions
    ): Promise<IResponse<ISearchResult>> {
        try {
            if (!words || words.length === 0 || !roomIds || roomIds.length === 0) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Search words and room IDs are required'
                });
            }

            const result = await this.messageModel.searchMessagesInRooms(words, roomIds, filters, options);
            return this.messageModel.formatResponse<ISearchResult>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Messages searched successfully across rooms'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to search messages across rooms'
            });
        }
    }

    /**
     * Global search across all rooms a user has access to
     */
    async globalMessageSearch(
        userId: string,
        words: string[],
        roomIds?: string[],
        filters?: ISearchFilters,
        options?: ISearchOptions
    ): Promise<IResponse<ISearchResult>> {
        try {
            if (!userId || !words || words.length === 0) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'User ID and search words are required'
                });
            }

            const result = await this.messageModel.globalMessageSearch(userId, words, roomIds || [], filters, options);
            return this.messageModel.formatResponse<ISearchResult>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Global message search completed successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to perform global message search'
            });
        }
    }

    /**
     * Search messages by sender
     */
    async searchMessagesBySender(
        senderId: string,
        roomIds?: string[],
        filters?: Omit<ISearchFilters, 'senderId'>,
        options?: ISearchOptions
    ): Promise<IResponse<ISearchResult>> {
        try {
            if (!senderId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Sender ID is required'
                });
            }

            const result = await this.messageModel.searchMessagesBySender(senderId, roomIds, filters, options);
            return this.messageModel.formatResponse<ISearchResult>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Messages by sender retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to search messages by sender'
            });
        }
    }

    /**
     * Search messages with files
     */
    async searchMessagesWithFiles(
        roomIds: string[],
        fileTypes?: string[],
        filters?: ISearchFilters,
        options?: ISearchOptions
    ): Promise<IResponse<ISearchResult>> {
        try {
            if (!roomIds || roomIds.length === 0) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Room IDs are required'
                });
            }

            const result = await this.messageModel.searchMessagesWithFiles(roomIds, fileTypes, filters, options);
            return this.messageModel.formatResponse<ISearchResult>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Messages with files retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to search messages with files'
            });
        }
    }

    // ============= MESSAGE RETRIEVAL FUNCTIONALITY =============

    /**
     * Get recent messages from a room
     */
    async getRecentMessages(
        roomId: string,
        limit: number = 50,
        before?: Date
    ): Promise<IResponse<IMessage[]>> {
        try {
            if (!roomId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Room ID is required'
                });
            }

            const result = await this.messageModel.getRecentMessages(roomId, limit, before);
            return this.messageModel.formatResponse<IMessage[]>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Recent messages retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get recent messages'
            });
        }
    }

    /**
     * Get message by ID
     */
    async getMessageById(id: string): Promise<IResponse<IMessage | null>> {
        try {
            if (!id) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Message ID is required'
                });
            }

            const result = await this.messageModel.getMessageById(id);
            if (!result) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.notFoundParams,
                    message: 'Message not found'
                });
            }

            return this.messageModel.formatResponse<IMessage>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get message'
            });
        }
    }

    /**
     * Get unread messages count for a user
     */
    async getUnreadMessagesCount(userId: string, roomIds?: string[]): Promise<IResponse<number>> {
        try {
            if (!userId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'User ID is required'
                });
            }

            const result = await this.messageModel.getUnreadMessagesCount(userId, roomIds);
            return this.messageModel.formatResponse<number>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Unread messages count retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get unread messages count'
            });
        }
    }

    async getUnreadMessages(userId: string, roomIds?: string[]): Promise<IResponse<IMessage[]>> {
        try {
            if (!userId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'User ID is required'
                });
            }

            const result = await this.messageModel.getUnreadMessages(userId, roomIds);
            return this.messageModel.formatResponse<IMessage[]>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Unread messages retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get unread messages'
            });
        }
    }

    // ============= BULK OPERATIONS =============

    /**
     * Mark multiple messages as read
     */
    async markMultipleMessagesRead(messageIds: string[]): Promise<IResponse<boolean>> {
        try {
            if (!messageIds || messageIds.length === 0) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Message IDs are required'
                });
            }

            const result = await this.messageModel.markMessageRead(messageIds);
            return this.messageModel.formatResponse<boolean>({
                ...this.messageModel.successParams,
                data: result,
                message: `${messageIds.length} messages marked as read successfully`
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to mark multiple messages as read'
            });
        }
    }

    /**
     * Pin/Unpin a message
     */
    async toggleMessagePin(messageId: string, isPinned: boolean, userId: string): Promise<IResponse<IMessage | null>> {
        try {
            if (!messageId || !userId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Message ID and User ID are required'
                });
            }

            // Check if user has permission to pin messages
            const message = await this.messageModel.getMessageById(messageId);
            if (!message) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.notFoundParams,
                    message: 'Message not found'
                });
            }

            // TODO: Add permission check with room service
            // const hasPermission = await this.rmService.checkUserPermission(userId, message.to, IRoomPermissionType.CAN_PIN_MESSAGES);
            // if (!hasPermission) {
            //     return this.messageModel.formatResponse<null>({
            //         ...this.messageModel.forbiddenParams,
            //         message: 'You do not have permission to pin messages in this room'
            //     });
            // }

            const result = await this.messageModel.updateMessage(messageId, { isPinned });
            return this.messageModel.formatResponse<IMessage>({
                ...this.messageModel.successParams,
                data: {...message, isPinned },
                message: `Message ${isPinned ? 'pinned' : 'unpinned'} successfully`
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to toggle message pin status'
            });
        }
    }

    /**
     * Mark message as answered/unanswered
     */
    async toggleMessageAnswered(messageId: string, isAnswered: boolean, userId: string): Promise<IResponse<IMessage | null>> {
        try {
            if (!messageId || !userId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Message ID and User ID are required'
                });
            }

            const message = await this.messageModel.getMessageById(messageId);
            if (!message) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.notFoundParams,
                    message: 'Message not found'
                });
            }

            const result = await this.messageModel.updateMessage(messageId, { isAnswered });
            return this.messageModel.formatResponse<IMessage>({
                ...this.messageModel.successParams,
                data: {...message, isAnswered },
                message: `Message marked as ${isAnswered ? 'answered' : 'unanswered'} successfully`
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to toggle message answered status'
            });
        }
    }

    // ============= ANALYTICS AND STATISTICS =============

    /**
     * Get message statistics for a room or multiple rooms
     * Note: This would require implementing the getMessageStats method in the Message model
     */
    async getMessageStatistics(roomIds: string[], dateFrom?: Date, dateTo?: Date): Promise<IResponse<IMessageStats>> {
        try {
            if (!roomIds || roomIds.length === 0) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Room IDs are required'
                });
            }

            // This would need to be implemented in the Message model
            // const result = await this.messageModel.getMessageStats(roomIds, dateFrom, dateTo);
            
            // For now, return a placeholder response
            const result: IMessageStats = {
                totalMessages: 0,
                messagesByStatus: { sent: 0, delivered: 0, read: 0 },
                messagesByType: { text: 0, withFiles: 0, threads: 0, pinned: 0 },
                topSenders: [],
                messagesOverTime: []
            };

            return this.messageModel.formatResponse<IMessageStats>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message statistics retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get message statistics'
            });
        }
    }

    /**
     * Get room activity summary
     * Note: Still thinking if I need to implement the getRoomActivity method in the Message model
     */
    async getRoomActivity(roomIds: string[], userId: string): Promise<IResponse<IMessageActivity[]>> {
        try {
            if (!roomIds || roomIds.length === 0 || !userId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Room IDs and User ID are required'
                });
            }

            // This would need to be implemented in the Message model
            // const result = await this.messageModel.getRoomActivity(roomIds, userId);
            
            // For now, return a placeholder response
            const result: IMessageActivity[] = [];

            return this.messageModel.formatResponse<IMessageActivity[]>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Room activity retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to get room activity'
            });
        }
    }
}