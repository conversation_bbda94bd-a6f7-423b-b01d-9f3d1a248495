import { <PERSON><PERSON>, Guest, <PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON>, IGuestMetaData } from "../../models";
import { AuthService, IResponse } from "../../utils";

export class GuestService {
    constructor(private readonly tokenModel: Token, private readonly guestModel: Guest, private readonly accessKeyModel: AccessKey) {
    }

    async upsert(externalId: string, companyId: string, metaData: IGuestMetaData): Promise<IResponse<IGuest | null>> {
      try{
        const guest = await this.guestModel.upsert(externalId, companyId, metaData);;
        return this.guestModel.formatResponse<IGuest>({...this.guestModel.successParams,  message: 'Guest upserted successfully', data: guest});
      } catch(error: any){
        if(error.message && error.message.includes('Validation failed')){
          return this.guestModel.formatResponse<null>({...this.guestModel.badRequestParams,  message: error.message});
        }
        return this.guestModel.formatResponse<null>({...this.guestModel.internalServerErrorParams,  message: error.message});
      }
    }

    async loginGuest(id: string, apiKey: string): Promise<IResponse<{token: string, data: IGuest} | null>> {
      try{

        if (!id || !apiKey) {
          return this.guestModel.formatResponse<null>({
            ...this.guestModel.badRequestParams,
            data: null,
            message: 'Missing the X-API-KEY header or the id field in the request body',
          });
        }

         const company = await this.accessKeyModel.getCompanyByApiKey(apiKey);
         if (!company) {
          return this.guestModel.formatResponse<null>({...this.guestModel.badRequestParams, data: null, message: 'Invalid API key'});
         }

         let guest: IGuest | null = null;
         
         if(id) {
          guest = await this.guestModel.getGuestByExternalIdAndCompanyId(id, company.id);
         }
         
         if(!guest) {
          guest = await this.guestModel.getGuestByIdAndCompanyId(id, company.id);
         }
        
         if(!guest) {
          return this.guestModel.formatResponse<null>({...this.guestModel.notFoundParams, data: null, message: 'Guest not found'});
        }
        
        const sparkstrand_token = AuthService.generateToken({
          id: guest.id,
          companyId: company.id,
          type: 'guest',
          isAuthenticated: true
        });

   
        return this.guestModel.formatResponse<{token: string, data: IGuest}>(
          {
            ...this.guestModel.successParams, 
            data: { token: sparkstrand_token, data: guest },
            message: 'Login successful.'
          }
        );

      } catch(error: any){
        if(error.message && error.message.includes('Validation failed')){
          return this.guestModel.formatResponse<null>({...this.guestModel.badRequestParams,  message: error.message});
        }
        return this.guestModel.formatResponse<null>({...this.guestModel.internalServerErrorParams,  message: error?.message || 'Failed to login'});
      }
    }

    async getGuestById(guestId: string): Promise<IResponse<IGuest | null>> {
      try {
        const guest = await this.guestModel.getGuestById(guestId);
        return this.guestModel.formatResponse<IGuest | null>({
          ...(guest ? this.guestModel.successParams : this.guestModel.notFoundParams), 
          data: guest,
          message: guest ? 'Guest retrieved successfully' : 'Guest not found'
        });
      } catch (error: any) {
        if(error.message && error.message.includes('Validation failed')) {
          return this.guestModel.formatResponse<null>({
            ...this.guestModel.badRequestParams,
            message: error.message
          });
        }
        return this.guestModel.formatResponse<null>({
          ...this.guestModel.internalServerErrorParams,
          message: error.message || 'Failed to retrieve guest'
        });
      }
    }

    async getGuestByExternalIdAndCompanyId(externalId: string, companyId: string): Promise<IResponse<IGuest | null>> {
      try {
        const guest = await this.guestModel.getGuestByExternalIdAndCompanyId(externalId, companyId);
        return this.guestModel.formatResponse<IGuest | null>({
          ...(guest ? this.guestModel.successParams : this.guestModel.notFoundParams), 
          data: guest,
          message: guest ? 'Guest retrieved successfully' : 'Guest not found'
        });
      } catch (error: any) {
        if(error.message && error.message.includes('Validation failed')) {
          return this.guestModel.formatResponse<null>({
            ...this.guestModel.badRequestParams,
            message: error.message
          });
        }
        return this.guestModel.formatResponse<null>({
          ...this.guestModel.internalServerErrorParams,
          message: error.message || 'Failed to retrieve guest'
        });
      }
    }

    async getGuestByIdAndCompanyId(guestId: string, companyId: string): Promise<IResponse<IGuest | null>> {
      try {
        const guest = await this.guestModel.getGuestByIdAndCompanyId(guestId, companyId);
        return this.guestModel.formatResponse<IGuest | null>({
          ...(guest ? this.guestModel.successParams : this.guestModel.notFoundParams), 
          data: guest,
          message: guest ? 'Guest retrieved successfully' : 'Guest not found'
        });
      } catch (error: any) {
        if(error.message && error.message.includes('Validation failed')) {
          return this.guestModel.formatResponse<null>({
            ...this.guestModel.badRequestParams,
            message: error.message
          });
        }
        return this.guestModel.formatResponse<null>({
          ...this.guestModel.internalServerErrorParams,
          message: error.message || 'Failed to retrieve guest'
        });
      }
    }

    async updateLastSeen(guestId: string, date: Date = new Date()): Promise<IResponse<IGuest | null>> {
    try{
      const result = await this.guestModel.updateGuest(guestId, {lastSeenAt: date});
      return this.guestModel.formatResponse<IGuest | null>({...this.guestModel.successParams, data: result, message: 'Last seen updated successfully'});
    } catch(error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.guestModel.formatResponse<null>({...this.guestModel.badRequestParams, message: error.message});
      }
      return this.guestModel.formatResponse<null>({...this.guestModel.internalServerErrorParams, message: error.message});
    }
    }

    async updateGuest(guestId: string, data: any): Promise<IResponse<IGuest | null>> {
      try{
        const result = await this.guestModel.updateGuest(guestId, data);
        return this.guestModel.formatResponse<IGuest | null>({...this.guestModel.successParams, data: result, message: 'Guest updated successfully'});
      } catch(error: any) {
        if(error.message && error.message.includes('Validation failed')) {
          return this.guestModel.formatResponse<null>({...this.guestModel.badRequestParams, message: error.message});
        }
        return this.guestModel.formatResponse<null>({...this.guestModel.internalServerErrorParams, message: error.message});
      }
    }

    async guestRoomActivity(guestId: string, roomId: string, messageId?: string): Promise<IResponse<void>> {
      try{
        await this.guestModel.guestRoomActivity(guestId, roomId, messageId);
        return this.guestModel.formatResponse<void>({...this.guestModel.successParams, data: null, message: 'Guest room activity updated successfully'});
      } catch(error: any) {
        if(error.message && error.message.includes('Validation failed')) {
          return this.guestModel.formatResponse<null>({...this.guestModel.badRequestParams, message: error.message});
        }
        return this.guestModel.formatResponse<null>({...this.guestModel.internalServerErrorParams, message: error.message});
      }
    }
}
