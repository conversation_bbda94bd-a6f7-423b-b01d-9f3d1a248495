
import { MessageService } from "../services";
import { ControllerHelper, AuthPayload, IRequest } from "../../utils";
import { Response, NextFunction } from "express";
import { ICreateMessage } from "../../models";

interface IMessageSearch {
    words?: string[];
    roomId?: string;
    guestId?: string; // the guest making the query
    messageId?: string;
    roomIds?: string;
    filters?: {
        senderId?: string;
        senderType?: 'admin' | 'chatAgent' | 'aiAgent' | 'guest';
        dateFrom?: string;
        dateTo?: string;
        hasFiles?: boolean;
        fileTypes?: string[];
        isPinned?: boolean;
        isAnswered?: boolean;
        status?: "Sent" | "Delivered" | "Read";
        parentId?: string;
        isThread?: boolean;
        isEncrypted?: boolean;
        isEdited?: boolean;
        isRead?: boolean;
    };
    options?: {
        limit?: number;
        offset?: number;
        sortBy?: 'createdAt' | 'updatedAt' | 'relevance';
        sortOrder?: 'asc' | 'desc';
        includeContext?: boolean;
        highlightMatches?: boolean;
        };
}

export class MessageController {
    constructor(private readonly msgService: MessageService, private readonly controllerHelper: ControllerHelper) {
    }

    async Create(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const isRestClient = this.controllerHelper.isRestClient(request);
            let sender: Partial<AuthPayload>;
            const body = this.controllerHelper.validateRequestBody<ICreateMessage>(request, 'Create message controller');
            if(!isRestClient){
                sender = this.controllerHelper.validateUser(request, 'Create message controller');
                body.senderId = sender.id;
            }
            const result =  await this.msgService.createMessage(body);
            this.controllerHelper.handleServiceResult(result, response, "Create message controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async Edit(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const messageId = request.params.messageId;
            const isRestClient = this.controllerHelper.isRestClient(request);
            const body = this.controllerHelper.validateRequestBody<{text: string, messageId: string, guestId?: string}>(request, 'Edit message controller');
            if(!isRestClient){
                 const updator = this.controllerHelper.validateUser(request, 'Create message controller');
                body.guestId = updator.id;
            }
            const result =  await this.msgService.editMessageText(messageId, body.text, body.guestId);
            this.controllerHelper.handleServiceResult(result, response, "Edit message controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async Delete(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const messageId = request.params.messageId;
            let deleterId = request.body?.guestId || request.query?.guestId;
            const isRestClient = this.controllerHelper.isRestClient(request);
            if(!isRestClient){
                const user = this.controllerHelper.validateUser(request, 'Delete message controller');
                deleterId = user.id;
            }
            const result =  await this.msgService.deleteMessage(messageId, deleterId);
            this.controllerHelper.handleServiceResult(result, response, "Delete message controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async MarkAsRead(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const body = this.controllerHelper.validateRequestBody<{messageIds: string[], guestId?: string}>(request, 'Mark message as read controller');
            const ids = body.messageIds;
            // const isRestClient = this.controllerHelper.isRestClient(request);
            // if(!isRestClient){
            //     const user = this.controllerHelper.validateUser(request, 'Mark message as read controller');
            //     body.guestId = user.id;
            // }
            const result =  await this.msgService.markMessageRead(ids);
            this.controllerHelper.handleServiceResult(result, response, "Mark message as read controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async CreateThread(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const body = this.controllerHelper.validateRequestBody<ICreateMessage>(request, 'Create thread controller');
            const parentId = request.params.parentId;
            const isRestClient = this.controllerHelper.isRestClient(request);
            if(!isRestClient){
                const user = this.controllerHelper.validateUser(request, 'Create thread controller');
                body.senderId = user.id;
            }else{
                body.senderId = request.body?.guestId || request.query?.guestId;
            }
            const result =  await this.msgService.createThread(parentId, body);
            this.controllerHelper.handleServiceResult(result, response, "Create thread controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetThread(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const messageId = request.params.messageId;
            const result =  await this.msgService.getThread(messageId);
            this.controllerHelper.handleServiceResult(result, response, "Get thread controller"); 
        } catch(error: any){
            next(error);
        }
    }

    /**
     * Will only be used by admins of a company
     */
    async searchInRoom(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const { words, filters, options, roomId } = this.controllerHelper.validateRequestBody<IMessageSearch>(request, 'SearchInRoom controller');
            if(!roomId){
                return this.controllerHelper.handleServiceResult(
                    {
                        success: false,
                        statusCode: 400,
                        data: null,
                        message: 'Room ID is required'
                    },
                    response,
                    'SearchInRoom controller'
                   )
            }
            
            if (!words || !Array.isArray(words) || words.length === 0) {
               return this.controllerHelper.handleServiceResult(
                {
                    success: false,
                    statusCode: 400,
                    data: null,
                    message: 'Search words are required'
                },
                response,
                'SearchInRoom controller'
               )
            }

            // Convert date strings to Date objects if provided
            const processedFilters = filters ? {
                ...filters,
                dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
                dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined
            } : undefined;

            const result = await this.msgService.searchMessagesInRoom(roomId, words, processedFilters, options);
            this.controllerHelper.handleServiceResult(result, response, "SearchInRoom controller"); 
        } catch(error: any){
            next(error);
        }
    }

    /**Will only be used by admins of a company */
    async searchInMultipleRooms(request: IRequest, response: Response, next: NextFunction ): Promise<void> {
        try {
            const { words, filters, options, roomIds  } = this.controllerHelper.validateRequestBody<IMessageSearch>(request, 'SearchInMultipleRooms controller');
            
            if (!words || !Array.isArray(words) || words.length === 0) {
              return this.controllerHelper.handleServiceResult(
                {
                    success: false,
                    statusCode: 400,
                    data: null,
                    message: 'Search words are required'
                },
                response,
                'SearchInMultipleRooms controller'
               )
            }

            if (!roomIds) {
              return this.controllerHelper.handleServiceResult(
                {
                    success: false,
                    statusCode: 400,
                    data: null,
                    message: 'Room IDs are required'
                },
                response,
                'SearchInMultipleRooms controller'
               )
            }

            const roomIdArray = Array.isArray(roomIds) ? roomIds : roomIds.split(',');
            
            // Convert date strings to Date objects if provided
            const processedFilters = filters ? {
                ...filters,
                dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
                dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined
            } : undefined;

            const result = await this.msgService.searchMessagesInRooms(
                words, 
                roomIdArray, 
                processedFilters, 
                options
            );
            this.controllerHelper.handleServiceResult(result, response, "SearchInMultipleRooms controller"); 
        } catch(error: any){
            next(error);
        }
    }

    /**
     * Scope to a guest being a member in the rooms
     */
    async searchMessage(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try{
            const { words, filters, options, roomIds, guestId  } = this.controllerHelper.validateRequestBody<IMessageSearch>(request, 'searchMessage controller');
            if(!guestId){
                return this.controllerHelper.handleServiceResult(
                    {
                        success: false,
                        statusCode: 400,
                        data: null,
                        message: 'Guest ID is required'
                    },
                    response,
                    'searchMessage controller'
                    )
            }
            if (!words || !Array.isArray(words) || words.length === 0) {
                return this.controllerHelper.handleServiceResult(
                    {
                        success: false,
                        statusCode: 400,
                        data: null,
                        message: 'Search words are required'
                    },
                    response,
                    'searchMessage controller'
                    )
            }
            const roomIdArray = Array.isArray(roomIds) ? roomIds : roomIds.split(',');
            const processedFilters = filters ? {
                ...filters,
                dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
                dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined
            } : undefined;

            const result = await this.msgService.globalMessageSearch(guestId, words, roomIdArray, processedFilters, options);
            this.controllerHelper.handleServiceResult(result, response, "searchMessage controller");
        } catch(error: any){
            next(error);
        }
    }

    /**
     * Search messages from a user
     */
    async searchMessageFromUser(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try{
            const { filters, options, roomIds } = this.controllerHelper.validateRequestBody<IMessageSearch>(request, 'SearchMessageFromUser controller');
            const { senderId } = filters;
            if(!senderId){
                return this.controllerHelper.handleServiceResult(
                    {
                        success: false,
                        statusCode: 400,
                        data: null,
                        message: 'Sender ID is required'
                    },
                    response,
                    'SearchMessageFromUser controller'
                    )
            }
            const roomIdArray = Array.isArray(roomIds) ? roomIds : roomIds.split(',');
            const processedFilters = filters ? {
                ...filters,
                dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
                dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined
            } : undefined;

            const result = await this.msgService.searchMessagesBySender(senderId, roomIdArray, processedFilters, options);
            this.controllerHelper.handleServiceResult(result, response, "SearchMessageFromUser controller");
        } catch(error: any){
            next(error);
        }
    }
}