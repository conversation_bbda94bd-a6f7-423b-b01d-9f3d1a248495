import { NextFunction, Response } from 'express'
import { CompanyService } from '../services'
import { ControllerHelper, IResponse, IRequest } from '../../utils';
import { ICompany, ICreateCompanyAccesskey, ICreateCompanyData, IDeleteCompanyAccesskey, IUpdateCompanyAccesskey, ICreateCloudStorageProvider, IUpdateCloudStorageProvider } from '../../models';

type RoleName = { roleName: string };

export class CompanyController {

    constructor(private readonly companyService: CompanyService, private readonly controllerHelper: ControllerHelper) {
    
    };

    async Create(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Create company controller');
            const body = this.controllerHelper.validateRequestBody<ICreateCompanyData>(request, 'Create company controller');
            const result =  await this.companyService.Create(body, user);
            this.controllerHelper.handleServiceResult(result, response, "Create company controller"); 
        } catch(error: any){
            next(error);
        }
    }
    async Update(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Update company controller');
            const id = request.params.companyId;
            const body = this.controllerHelper.validateRequestBody<Partial<ICompany & RoleName>>(request, 'Update company controller');
            if(!body.roleName){
                const result: IResponse<null> = {success: false, statusCode: 400, data: null, message: 'Role name is required'};
                this.controllerHelper.handleServiceResult(result, response, 'Update company controller');
                 return;
            }
            const { roleName, ...rest } = body;
            const result =  await this.companyService.Update({ ...rest, id}, user, roleName);
            this.controllerHelper.handleServiceResult(result, response, "Update company controller");
        } catch(error: any){
            next(error);
        }
    }

    async Get(request: IRequest, response: Response, next: NextFunction): Promise<void>{
        try{
            const companyId = request.params.companyId;
            const result = await this.companyService.GetCompanyById(companyId);
            this.controllerHelper.handleServiceResult(result, response, "Get company controller");
        } catch(error: any){
            next(error);
        }
    }

    async GenerateAccessKey(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Generate access key controller');
            const companyId = request.params.companyId;
            const body = this.controllerHelper.validateRequestBody<Omit<ICreateCompanyAccesskey, 'companyId'>>(request, 'Generate access key controller');
            const result =  await this.companyService.GenerateAccessKey({...body, companyId}, user);
            this.controllerHelper.handleServiceResult(result, response, "Generate access key controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async UpdateAccessKey(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Update access key controller');
            const companyId = request.params.companyId;
            const body = this.controllerHelper.validateRequestBody<Omit<IUpdateCompanyAccesskey, 'companyId'>>(request, 'Update access key controller');
            const result =  await this.companyService.UpdateAccessKey({...body, companyId}, user);
            this.controllerHelper.handleServiceResult(result, response, "Update access key controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetAccessKey(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Get access key controller');
            const companyId = request.params.companyId;
            const body = this.controllerHelper.validateRequestBody<{key: string, accountId: string}>(request, 'Get access key controller');
            const result =  await this.companyService.GetAccessKey({...body, companyId}, user);
            this.controllerHelper.handleServiceResult(result, response, "Get access key controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async DeleteAccessKey(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Delete access key controller');
            const companyId = request.params.companyId;
            const body = this.controllerHelper.validateRequestBody<Omit<IDeleteCompanyAccesskey, 'companyId'>>(request, 'Delete access key controller');
            const result =  await this.companyService.DeleteAccessKey({...body, companyId}, user);
            this.controllerHelper.handleServiceResult(result, response, "Delete access key controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async UpdateCompanyDomains(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Update company domains controller');
            const companyId = request.params.companyId;
            const body = this.controllerHelper.validateRequestBody<{domainsToAdd: string[], domainsToRemove: string[]}>(request, 'Update company domains controller');
            const result =  await this.companyService.UpdateCompanyDomains(companyId, body.domainsToAdd, body.domainsToRemove);
            this.controllerHelper.handleServiceResult(result, response, "Update company domains controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async createCloudStorageProvider(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Create cloud storage provider controller');
            const companyId = request.params.companyId;
            const body = this.controllerHelper.validateRequestBody<ICreateCloudStorageProvider>(request, 'Create cloud storage provider controller');
            const result =  await this.companyService.createCloudStorageProvider({...body, companyId});
            this.controllerHelper.handleServiceResult(result, response, "Create cloud storage provider controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async updateCloudStorageProvider(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const cloudStorageProviderId = request.params.storageId;
            const body = this.controllerHelper.validateRequestBody<IUpdateCloudStorageProvider>(request, 'Update cloud storage provider controller');
            const result =  await this.companyService.updateCloudStorageProvider(cloudStorageProviderId, body);
            this.controllerHelper.handleServiceResult(result, response, "Update cloud storage provider controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getCompanyPrimaryCloudStorageProvider(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const companyId = request.params.companyId;
            const result =  await this.companyService.getPrimaryCloudStorageProvider(companyId);
            this.controllerHelper.handleServiceResult(result, response, "Get primary cloud storage provider controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getCompanyActiveCloudStorageProviders(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const companyId = request.params.companyId;
            const result =  await this.companyService.getActiveCloudStorageProviders(companyId);
            this.controllerHelper.handleServiceResult(result, response, "Get active cloud storage providers controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getCompanyRevokedCloudStorageProviders(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const companyId = request.params.companyId;
            const result =  await this.companyService.getRevokedCloudStorageProviders(companyId);
            this.controllerHelper.handleServiceResult(result, response, "Get revoked cloud storage providers controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getCompanyListOfCloudStorageProviders(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const companyId = request.params.companyId;
            const result =  await this.companyService.getCloudStorageProvidersByCompanyId(companyId);
            this.controllerHelper.handleServiceResult(result, response, "Get cloud storage providers by company id controller"); 
        } catch(error: any){
            next(error);
        }
    }
}
