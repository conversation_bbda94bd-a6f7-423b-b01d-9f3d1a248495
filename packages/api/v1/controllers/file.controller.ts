import { NextFunction, Response } from 'express';
import { FileService } from '../services';
import { ControllerHelper, IRequest } from '../../utils';
import {  ICreateFile, IUpdateFile, } from '../../models';



export class FileController {
    constructor(private readonly fileService: FileService, private readonly controllerHelper: ControllerHelper) {
    }

    async createFile(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try {
            const user = this.controllerHelper.validateUser(request, 'Create file controller');
            const body = this.controllerHelper.validateRequestBody<ICreateFile>(request, 'Create file controller');
            const provider = request?.provider;
            const files = request.files;
            const data = files.map(file => ({
                filename: file.originalname,
                fileUrl: file.location || file.path,
                fileType: file.mimetype,
                size: file.size,
                key: file.key || file.filename,
                uploadedBy: user.id,
                providerId: provider?.id,
                messageId: body?.messageId,
                roomId: body?.roomId,
            }));
            
            const result = await this.fileService.createFile(data);
            this.controllerHelper.handleServiceResult(result, response, 'Create file controller');
        } catch (error: any) {
            next(error);
        }
    }

    async updateFile(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = request.params;
            const body = this.controllerHelper.validateRequestBody<IUpdateFile>(request, 'Update file controller');
            const result = await this.fileService.updateFile(id, body);
            this.controllerHelper.handleServiceResult(result, response, 'Update file controller');
        } catch (error: any) {
            next(error);
        }
    }

    async uploadGuestFile(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try {
            const user = this.controllerHelper.validateUser(request, 'Upload file controller');
            const roomId = request.body?.roomId;
            const provider = request?.provider;
            const files = request.files;
            const data = files.map(file => ({
                filename: file.originalname,
                fileUrl: file.location || file.path,
                fileType: file.mimetype,
                size: file.size,
                key: file.key || file.filename,
                uploadedBy: user.id,
                providerId: provider?.id,
                roomId,
            }));
            const result = await this.fileService.handleGuestUpload(data);
            this.controllerHelper.handleServiceResult(result, response, 'Upload file controller');
        } catch (error: any) {
            next(error);
        }
    }
}


