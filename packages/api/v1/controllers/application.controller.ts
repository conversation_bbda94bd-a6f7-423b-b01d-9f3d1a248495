import { Response, NextFunction } from 'express';
import { ApplicationService } from '../services/application.service';
import { ControllerHelper, IRequest } from "../../utils";

export class ApplicationController {
  constructor(
    private readonly appService: ApplicationService,
    private readonly controllerHelper: ControllerH<PERSON><PERSON>,
  ) {}

  async createApplication(request: IRequest, response: Response, next: NextFunction): Promise<void> {
    try {
      const body = this.controllerHelper.validateRequestBody(request, 'CreateApplication controller');
      const result = await this.appService.createApplication(body);
      this.controllerHelper.handleServiceResult(result, response, 'CreateApplication controller');
    } catch (error: any) {
      next(error);
    }
  }

  async getApplicationById(request: IRequest, response: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = request.params;
      const result = await this.appService.getApplicationById(id);
      this.controllerHelper.handleServiceResult(result, response, 'GetApplicationById controller');
    } catch (error: any) {
      next(error);
    }
  }

  async getApplicationsByCompanyId(request: IRequest, response: Response, next: NextFunction): Promise<void> {
    try {
      const { companyId } = request.params;
      const result = await this.appService.getApplicationsByCompanyId(companyId);
      this.controllerHelper.handleServiceResult(result, response, 'GetApplicationsByCompanyId controller');
    } catch (error: any) {
      next(error);
    }
  }

  async updateApplication(request: IRequest, response: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = request.params;
      const body = this.controllerHelper.validateRequestBody(request, 'UpdateApplication controller');
      const result = await this.appService.updateApplication(id, body);
      this.controllerHelper.handleServiceResult(result, response, 'UpdateApplication controller');
    } catch (error: any) {
      next(error);
    }
  }

  async deleteApplication(request: IRequest, response: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = request.params;
      const result = await this.appService.deleteApplication(id);
      this.controllerHelper.handleServiceResult(result, response, 'DeleteApplication controller');
    } catch (error: any) {
      next(error);
    }
  }
}