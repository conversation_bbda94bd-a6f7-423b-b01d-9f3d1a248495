import { NextFunction,  Response } from 'express'
import { GuestService, RoomService } from '../services'
import { AuthService, ControllerHelper, IRequest } from '../../utils'
import { ICreateRoom, RoomType } from '../../models';


export class GuestController {
    constructor(private readonly gtService: GuestService, private readonly rmService: RoomService, private readonly controllerHelper: ControllerHelper) {
    }

    async upsertGuest(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try {
            const body = this.controllerHelper.validateRequestBody<{metaData: any, apiKey?: string, apiKeySecret?: string}>(request, 'UpsertGuest controller');
            const apiKey = request.headers['x-api-key']  || request.headers['X-API-Key'] || request.body?.apiKey;
            const apiKeySecret = request.headers['x-api-secret']  || request.headers['X-API-Secret'] || request.body?.apiKeySecret;
            let appInstance: any;
            let metaData = body.metaData;
            if(!metaData?.applicationId){
                appInstance = await AuthService.getAppTypeFromApiKeySecret(apiKey, apiKeySecret, 'General')
        
                if(!appInstance) {
                    return this.controllerHelper.handleServiceResult({ success: false, statusCode: 400, data: null, message: 'Invalid API key or secret' }, response, 'UpsertGuest controller');
                }
                metaData = {...metaData, applicationId: appInstance.id};
            }else{
                const companyApp = await AuthService.getCompanyAndAppsFromApiKeySecret(apiKey, apiKeySecret);
                if(!companyApp) {
                    return this.controllerHelper.handleServiceResult({ success: false, statusCode: 400, data: null, message: 'Invalid Api key or secret' }, response, 'UpsertGuest controller');
                }
                const app = companyApp.Company.applications.find(app => app.id === metaData.applicationId);
                if(!app) {
                    return this.controllerHelper.handleServiceResult({ success: false, statusCode: 400, data: null, message: 'Invalid applicationId' }, response, 'UpsertGuest controller');
                }
                appInstance = { id: app.id, companyId: companyApp.Company.id };
            }
            const { externalId } = metaData;
            const result = await this.gtService.upsert(externalId, appInstance.companyId, metaData);
            this.controllerHelper.handleServiceResult(result, response, 'UpsertGuest controller');
        } catch (error: any) {
            next(error);
        }
    }

    async loginGuest(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try {
            const body = this.controllerHelper.validateRequestBody<{Id?: string, id?: string, apiKey?: string}>(request, 'LoginGuest controller');
            const apiKey = request.headers['x-api-key']  || request.headers['X-API-Key'] || request.body?.apiKey;
            const result = await this.gtService.loginGuest(body?.Id || body?.id, apiKey as string);
            if(result.success && request.headers['RestClient'] && request.headers['RestClient'] === 'true') {
                return this.controllerHelper.handleServiceResult(result, response, 'LoginGuest controller');
            }
            this.controllerHelper.handleServiceResultWithCookie(result, response, true, 'LoginGuest controller');
        } catch (error: any) {
            next(error);
        }
    }

    async logoutGuest(request: IRequest, response: Response, next: NextFunction): Promise<void> {
        try {
            this.controllerHelper.clearCookie(response, true);
            this.controllerHelper.handleServiceResult({ success: true, statusCode: 200, data: null, message: 'Logged out successfully' }, response, 'Logout guest controller');
        } catch (error: any) {
            next(error);
        }
    };

    async CreateRoom(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const {apiKey, apiKeySecret} = this.controllerHelper.extractApikeysAndSecret(request);
            const isRestClient = this.controllerHelper.isRestClient(request);
            const body = this.controllerHelper.validateRequestBody<ICreateRoom>(request, 'CreateRoom controller');
            if(!isRestClient){
                const guest = this.controllerHelper.validateUser(request, 'CreateRoom controller');
                body.guestIds = body.type === RoomType.dm ? [...body.guestIds, guest.id] : body.guestIds;
            } else {
                if((apiKey && apiKeySecret) && !body?.applicationId){
                    const appInstance = await AuthService.getAppTypeFromApiKeySecret(apiKey, apiKeySecret, 'General');
                    body.applicationId = appInstance.id;
                }else {
                    return this.controllerHelper.handleServiceResult({ success: false, statusCode: 400, data: null, message: 'missing applicationId' }, response, 'CreateRoom controller');
                }
            }
            const result =  await this.rmService.createRoom(body);
            this.controllerHelper.handleServiceResult(result, response, "CreateRoom controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async UpdateRoom(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const isRestClient = this.controllerHelper.isRestClient(request);
            const body = this.controllerHelper.validateRequestBody(request, 'UpdateRoom controller');
            if(!isRestClient){
                this.controllerHelper.validateUser(request, 'UpdateRoom controller');
            }
            const roomId = request.params.roomId;
            const result =  await this.rmService.updateRoom(roomId, body);
            this.controllerHelper.handleServiceResult(result, response, "UpdateRoom controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestRooms(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const isRestClient = this.controllerHelper.isRestClient(request);
            let guestId: string = '';
            if(!isRestClient){
                const guest = this.controllerHelper.validateUser(request, 'GetGuestRooms controller');
                guestId = guest.id;
            }else{
                guestId = request.query?.guestId || request.body?.guestId || '';
            }
            const result =  await this.rmService.getRoomsByGuestId(guestId);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestRooms controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestRoomsByApplicationId(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const isRestClient = this.controllerHelper.isRestClient(request);
            let guestId: string = '';
            if(!isRestClient){
                const guest = this.controllerHelper.validateUser(request, 'GetGuestRoomsByApplicationId controller');
                guestId = guest.id;
            }else{
                guestId = request.query?.guestId || request.body?.guestId || '';
            }
            const applicationId = request.params?.applicationId || request.body?.applicationId;
            const result =  await this.rmService.getRoomsByGuestIdAndApplicationId(guestId, applicationId);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestRoomsByApplicationId controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestRoomsByApplicationIdAndCompanyId(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'getGuestRoomsByApplicationIdAndCompanyId controller');
            const applicationId = request.params?.applicationId || request.body?.applicationId;
            const result =  await this.rmService.getRoomsByGuestIdAndAppIdAndCompanyId(guest.id, applicationId, guest.companyId);
            this.controllerHelper.handleServiceResult(result, response, "getGuestRoomsByApplicationIdAndCompanyId controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async isGuestMemberOfRoom(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const isRestClient = this.controllerHelper.isRestClient(request);
            let guestId = null;
            if(!isRestClient){
                const guest = this.controllerHelper.validateUser(request, 'IsGuestMemberOfRoom controller');
                guestId = guest.id;
            }else{
                guestId = request.query?.guestId || request.body?.guestId;
            }
            const roomId = request.params?.roomId || request.body?.roomId;
            const result =  await this.rmService.isGuestMemberOfRoom(guestId, roomId);
            this.controllerHelper.handleServiceResult(result, response, "IsGuestMemberOfRoom controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestByExternalIdAndCompanyId(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const {apiKey, apiKeySecret} = this.controllerHelper.extractApikeysAndSecret(request);
            const isRestClient = this.controllerHelper.isRestClient(request);
            let companyId: string = ''
            if(!isRestClient){
                const guest = this.controllerHelper.validateUser(request, 'GetGuestByExternalIdAndCompanyId controller');
                companyId = guest.companyId;
            }else{
                const companyApp = await AuthService.getCompanyAndAppsFromApiKeySecret(apiKey, apiKeySecret);
                if(!companyApp) {
                    return this.controllerHelper.handleServiceResult({ success: false, statusCode: 400, data: null, message: 'Invalid Api key or secret' }, response, 'GetGuestByExternalIdAndCompanyId controller');
                }
                companyId = companyApp.Company.id;
            }
            const externalId = request.params?.externalId || request.body?.externalId;
            const result =  await this.gtService.getGuestByExternalIdAndCompanyId(externalId, companyId);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestByExternalIdAndCompanyId controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestById(request: IRequest, response: Response, next: NextFunction ): Promise<void>{
        try{
            const isRestClient = this.controllerHelper.isRestClient(request);
            let guestId = null
            if(!isRestClient){
                const guest = this.controllerHelper.validateUser(request, 'GetGuestById controller');
                guestId = guest.id;
            }else{
                guestId = request.query?.guestId || request.body?.guestId;
            }
            const result =  await this.gtService.getGuestById(guestId);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestById controller"); 
        } catch(error: any){
            next(error);
        }
    }
}
