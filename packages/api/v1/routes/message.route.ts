import { Router } from "express";
import { container } from "../container";
import { MessageController } from "../controllers";
import { IRequest } from "../../utils";

class MessageRouter {
    private router: Router;

    constructor(private readonly messageController: MessageController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/", (req, res, next)=> { this.messageController.Create(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:messageId", (req, res, next)=> { this.messageController.Edit(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:messageId", (req, res, next)=> { this.messageController.Delete(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/markAsRead", (req, res, next)=> { this.messageController.MarkAsRead(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/thread/:parentId", (req, res, next)=> { this.messageController.CreateThread(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/thread/:messageId", (req, res, next)=> { this.messageController.GetThread(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/search/room", (req, res, next)=> { this.messageController.searchInRoom(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/search/rooms", (req, res, next)=> { this.messageController.searchInMultipleRooms(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/search/words", (req, res, next)=> { this.messageController.searchMessage(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/search/sender", (req, res, next)=> { this.messageController.searchMessageFromUser(req as IRequest, res, next) });
    }

    public getRoutes(): Router {
        return this.router;
    }
}

/* Inject message dependencies */
const messageController = container.resolve<MessageController>("messageController");

export const messageRoute: MessageRouter =  new MessageRouter(messageController);