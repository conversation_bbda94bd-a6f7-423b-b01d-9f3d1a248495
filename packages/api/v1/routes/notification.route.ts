import { Router, Response } from "express";
import { In_App_Notification, IRequest } from "../../utils";
import { Server } from "socket.io";

class NotificationRouter {
    private router: Router;

    constructor() {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/in_app", In_App_Notification);

        /**
         * @swagger
         */
        this.router.get("/health", (req: IRequest, res: Response) => {
            const io: Server = req.app.get('io') as Server;
            const notificationNamespace = io.of('/notification');
            res.json({
                success: true,
                message: 'Notification Socket is healthy',
                activeConnections: notificationNamespace.sockets.size,
                timestamp: new Date().toISOString()
            });
        });
    }

    public getRoutes(): Router {
        return this.router;
    }
}

export const NotificationRoute: NotificationRouter =  new NotificationRouter();