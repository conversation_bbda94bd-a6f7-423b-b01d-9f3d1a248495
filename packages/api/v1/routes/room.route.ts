import { Router } from "express";
import { RoomController } from "../controllers";
import { container } from "../container";
import { IRequest } from "../../utils";


class RoomRouter {
    private router: Router;
    constructor(private readonly guestController: RoomController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/", (req, res, next)=> { this.guestController.Create(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId", (req, res, next)=> { this.guestController.Update(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId/addGuest", (req, res, next)=> { this.guestController.AddGuest(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId/addGuests", (req, res, next)=> { this.guestController.AddListOfGuests(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/guests", (req, res, next)=> { this.guestController.GetAllGuests(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/findFirst", (req, res, next)=> { this.guestController.FindRoom(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/findMany", (req, res, next)=> { this.guestController.FindRooms(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:roomId/guest", (req, res, next)=> { this.guestController.RemoveGuest(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:roomId/guests", (req, res, next)=> { this.guestController.RemoveListOfGuests(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/fullDetails", (req, res, next)=> { this.guestController.GetRoomFullDetails(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/media", (req, res, next)=> { this.guestController.GetRoomMedia(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/messages", (req, res, next)=> { this.guestController.GetRoomMessages(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId/metaData", (req, res, next)=> { this.guestController.UpdateRoomMetaData(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/metaData", (req, res, next)=> { this.guestController.GetRoomMetaData(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:roomId", (req, res, next)=> { this.guestController.DeleteRoom(req as IRequest, res, next) });
    }

    public getRoutes(): Router {
        return this.router;
    }
}

/* Inject company dependencies */
const roomController = container.resolve<RoomController>("roomController");

export const roomRoute: RoomRouter =  new RoomRouter(roomController);
