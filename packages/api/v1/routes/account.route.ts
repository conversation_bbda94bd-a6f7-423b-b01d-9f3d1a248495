import { Router } from "express";   
import { AccountController } from "../controllers";
import { container } from "../container";
import { IRequest } from "../../utils";


class AccountRouter {
    private router: Router;

    constructor(private readonly accountController: AccountController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/", (req, res, next)=> { this.accountController.Create(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:accountId", (req, res, next)=> { this.accountController.Update(req as IRequest, res, next) });


        /**
         * @swagger
         */
        this.router.delete("/:accountId", (req, res, next)=> { this.accountController.Delete(req as IRequest, res, next) });
    }

    public getRoutes(): Router {
        return this.router;
    }
}

/* Inject account dependencies */
const accountController = container.resolve<AccountController>("accountController");

export const accountRoute: AccountRouter =  new AccountRouter(accountController);