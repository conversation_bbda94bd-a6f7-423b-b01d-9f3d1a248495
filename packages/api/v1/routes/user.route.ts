
import { Router } from "express";
import { User<PERSON>ontroller} from "../controllers";
import { container } from "../container";
import { IRequest } from "../../utils";


export class UserRouter {
    private router: Router;

    constructor(private readonly userController: UserController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/signup", (req, res, next) => this.userController.Create(req as IRequest, res, next));

        /**
         * @swagger
         */
        this.router.post("/signin", (req, res, next) => this.userController.Login(req as IRequest, res, next));

        /**
         * @swagger
         */
        this.router.post("/signout", (req, res, next) => this.userController.Logout(req as IRequest, res, next));

        /**
         * 
         * @swagger
         */
        this.router.put("/update", (req, res, next) => this.userController.Update(req as IRequest, res, next));

        /**
         * 
         * @swagger
         */
        this.router.put("/password/change", (req, res, next) => this.userController.ChangePassword(req as IRequest, res, next));

        /**
         * 
         * @swagger
         */
        this.router.delete("/", (req, res, next) => this.userController.Delete(req as IRequest, res, next));

        
    }



    public getRoutes(): Router {
        return this.router;
    }
}


/* Inject dependencies */

const userController = container.resolve<UserController>("userController");

export const userRoute: UserRouter =  new UserRouter(userController);