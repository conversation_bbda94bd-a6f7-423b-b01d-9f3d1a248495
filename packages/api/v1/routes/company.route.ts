import { Router } from "express";
import { container } from "../container";
import { CompanyController } from "../controllers";
import { IRequest } from "../../utils";


class CompanyRouter {
    private router: Router;
    constructor(private readonly companyController: CompanyController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         * @param {name: string, website: string, industry: string, domains: string[], monthlySpend: number, location: {city: string, country: string, latitude: string, longitude: string}, tagIds: string[], userIds: string[], guestIds: string[], anonymousIds: string[], applicationsCount: number, usersCount: number, guestsCount: number, createdAt: Date, updatedAt: Date, accountId: string}
         */
        this.router.post("/", (req, res, next)=> { this.companyController.Create(req as IRequest, res, next) });

        /**
         * @swagger
         * @param {name: string, website: string, industry: string, domains: string[], monthlySpend: number, location: {city: string, country: string, latitude: string, longitude: string}, tagIds: string[], userIds: string[], guestIds: string[], anonymousIds: string[], applicationsCount: number, usersCount: number, guestsCount: number, createdAt: Date, updatedAt: Date, accountId: string}
         */
        this.router.put("/:companyId", (req, res, next)=> { this.companyController.Update(req as IRequest, res, next) });

        /**
         * @swagger
         * @param {name: string, status: string, expiresAt: Date, accountId: string, roleName: string}
         */
        this.router.post("/:companyId/accesskey", (req, res, next)=> { this.companyController.GenerateAccessKey(req as IRequest, res, next) });

        /**
         * @swagger
         * @param {name: string, status: string, expiresAt: Date, accountId: string, roleName: string}
         */
        this.router.put("/:companyId/accesskey", (req, res, next)=> { this.companyController.UpdateAccessKey(req as IRequest, res, next) });

        /**
         * @swagger
         * @param {apiKey: string, apiKeySecret: string}
         */
        this.router.delete("/:companyId/accesskey", (req, res, next)=> { this.companyController.DeleteAccessKey(req as IRequest, res, next) });

        /**
         * @swagger
         * @param {key: string, accountId: string}
         */
        this.router.get("/:companyId/accesskey", (req, res, next)=> { this.companyController.GetAccessKey(req as IRequest, res, next) });

        /**
         * @swagger
         * @param {id: string, name: string, website: string, industry: string, domains: string[], monthlySpend: number, location: {city: string, country: string, latitude: string, longitude: string}, tagIds: string[], userIds: string[], guestIds: string[], anonymousIds: string[], applicationsCount: number, usersCount: number, guestsCount: number, createdAt: Date, updatedAt: Date, accountId: string}
         */
        this.router.get("/:companyId", (req, res, next)=> { this.companyController.Get(req as IRequest, res, next) });

        /**
         * @swagger
         * @param {domainsToAdd: string[], domainsToRemove: string[]}
         */
        this.router.put("/:companyId/domains", (req, res, next)=> { this.companyController.UpdateCompanyDomains(req as IRequest, res, next) });

        this.router.post("/:companyId/storage", (req, res, next)=> { this.companyController.createCloudStorageProvider(req as IRequest, res, next) });

        this.router.put("/:companyId/storage/:storageId", (req, res, next)=> { this.companyController.updateCloudStorageProvider(req as IRequest, res, next) });

        this.router.get("/:companyId/storage/primary", (req, res, next)=> { this.companyController.getCompanyPrimaryCloudStorageProvider(req as IRequest, res, next) });

        this.router.get("/:companyId/storage/active", (req, res, next)=> { this.companyController.getCompanyActiveCloudStorageProviders(req as IRequest, res, next) });

        this.router.get("/:companyId/storage/revoke", (req, res, next)=> { this.companyController.getCompanyRevokedCloudStorageProviders(req as IRequest, res, next) });

        this.router.get("/:companyId/storage/list", (req, res, next)=> { this.companyController.getCompanyListOfCloudStorageProviders(req as IRequest, res, next) });

    }
    public getRoutes(): Router {
        return this.router;
    }
} 
/* Inject company dependencies */
const companyController = container.resolve<CompanyController>("companyController");

export const companyRoute: CompanyRouter =  new CompanyRouter(companyController);