import { Router } from "express";
import { Guest<PERSON>ontroller } from "../controllers";
import { container } from "../container";
import { IRequest } from "../../utils";

class GuestRouter {
    private router: Router;
    constructor(private readonly guestController: GuestController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/upsert", (req, res, next)=> { this.guestController.upsertGuest(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/login", (req, res, next)=> { this.guestController.loginGuest(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/logout", (req, res, next)=> { this.guestController.logoutGuest(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/", (req, res, next)=> { this.guestController.getGuestById(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.post("/rooms", (req, res, next)=> { this.guestController.CreateRoom(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.put("/rooms/:roomId", (req, res, next)=> { this.guestController.UpdateRoom(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/rooms", (req, res, next)=> { this.guestController.getGuestRooms(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/rooms/:applicationId", (req, res, next)=> { this.guestController.getGuestRoomsByApplicationId(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/rooms/:applicationId/:companyId", (req, res, next)=> { this.guestController.getGuestRoomsByApplicationIdAndCompanyId(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/rooms/:roomId/isMember", (req, res, next)=> { this.guestController.isGuestMemberOfRoom(req as IRequest, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:externalId", (req, res, next)=> { this.guestController.getGuestByExternalIdAndCompanyId(req as IRequest, res, next) });

        
    }

    public getRoutes(): Router {
        return this.router;
    }
} 

/* Inject guest dependencies */
const guestController = container.resolve<GuestController>("guestController");

export const guestRoute: GuestRouter =  new GuestRouter(guestController);