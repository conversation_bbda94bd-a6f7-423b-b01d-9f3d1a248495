import { Response, NextFunction } from "express";
import { IRequest } from "./auth";
import { ApiError } from "./error";
import { Server } from 'socket.io';

export const In_App_Notification = async (req: IRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Validate internal secret send by https://notifications.sparkstrand.com server
    const secret = req.headers['x-notification-secret'] || req.headers['X-Notification-Secret'];
    
    if (!secret || secret !== process.env.IN_APP_NOTIFICATION_COMMUNICATION_SECRET) {
      res.status(401).json({ 
        success: false, 
        message: 'Unauthorized' 
      });
      return;
    }
    
    const data = req.body;
    console.log('Notification data received:', data);
    
    // Validate required fields
    if (!data.recipientId) {
      res.status(400).json({ 
        success: false, 
        message: 'recipientId is required for in app notifications' 
      });
      return;
    }
    
    if (!data.title || !data.message) {
      res.status(400).json({ 
        success: false, 
        message: 'title and message are required for in app notifications' 
      });
      return;
    }
    
    const io: Server = req.app.get('io');
    if (!io) {
      console.error('Socket.IO instance not found');
      res.status(500).json({ 
        success: false, 
        message: 'Socket server not available' 
      });
      return;
    }
    
    console.log('Checking if recipient is online...');
    
    // Get the notification namespace
    const notificationNamespace = io.of('/notification');
    
    // Check if recipientId is online in the notification namespace
    const sockets = await notificationNamespace.in(data.recipientId.toString()).fetchSockets();
    console.log(`Found ${sockets.length} sockets for recipient ${data.recipientId}`);
    
    if (sockets.length === 0) {
      console.log(`User ${data.recipientId} is not online`);
      
      // TODO: Make a fetch request to https://notifications.sparkstrand.com/api/user/store-notification
      // to notify the server that the user is not online and save the in_app notification for later
      try {
        // const response = await fetch('https://notifications.sparkstrand.com/api/user/store-notification', {
        //   method: 'POST',
        //   headers: {
        //     'Content-Type': 'application/json',
        //     'Authorization': `Bearer ${process.env.NOTIFICATION_API_KEY}`
        //   },
        //   body: JSON.stringify({
        //        channel: 'in_app',
        //     recipientId: data.recipientId,
        //     title: data.title,
        //     message: data.message,
        //     type: data.type || 'general',
        //     timestamp: new Date().toISOString()
        //   })
        // });
        
        res.status(200).json({ 
          success: false, 
          message: 'User is not online', 
          stored: true 
        });
        return;
      } catch (error) {
        console.error('Error storing offline notification:', error);
        res.status(500).json({ 
          success: false, 
          message: 'Failed to store notification for offline user' 
        });
        return;
      }
    }
    
    console.log(`Sending in-app notification to online user ${data.recipientId}`);
    
    // Prepare notification payload
    const notificationPayload = {
      id: data.id || Date.now().toString(),
      title: data.title,
      body: data.message,
      type: data.type || 'general',
      timestamp: new Date().toISOString(),
      read: false
    };
    
    // Emit to in_app notification channel
    notificationNamespace.to(data.recipientId.toString()).emit('newNotification', notificationPayload);
    
    console.log('Notification sent successfully');
    
    res.status(200).json({ 
      success: true, 
      message: 'Notification sent successfully',
      data: {
        recipientId: data.recipientId,
        socketsReached: sockets.length
      }
    });
    
  } catch (error) {
    console.error('Error in In_App_Notification:', error);
    
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
      return
    }
    
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
    return;
  }
};