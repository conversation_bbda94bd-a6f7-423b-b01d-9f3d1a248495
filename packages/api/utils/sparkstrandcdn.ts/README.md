| `downloadAndStore(externalUrl, key, options?)` | Download from URL and store | externalUrl: string, key: string, options?: DownloadOptions | Promise\<DownloadResult\> |
| `downloadAndStoreWithAutoKey(externalUrl, folder?, options?)` | Download with auto-generated key | externalUrl: string, folder?: string, options?: DownloadOptions | Promise\<DownloadResult\> |
| `batchDownloadAndStore(downloads)` | Download multiple files | downloads: DownloadRequest[] | Promise\<BatchResult[]\> |# Cloudflare R2 Storage Utility

A TypeScript utility class for seamlessly integrating with Cloudflare R2 storage using the AWS S3-compatible API. Perfect for SparkStrand organization's file storage needs.

## 🚀 Features

- ✅ Upload files (Buffer, File, or from file path)
- ✅ Download files from R2 storage
- ✅ **Download from external URLs and store in R2**
- ✅ Delete files
- ✅ List files with filtering
- ✅ Generate public URLs using custom domain
- ✅ Create presigned URLs for temporary access
- ✅ File existence checking
- ✅ Automatic content-type detection
- ✅ Unique filename generation
- ✅ Batch operations for multiple files
- ✅ Full TypeScript support

## 📦 Installation

```bash
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
```

## 🔧 Setup

### 1. Environment Variables

Create a `.env` file in your project root:

```env
# Your Cloudflare R2 credentials
R2_ACCESS_KEY_ID=your_r2_access_key_here
R2_SECRET_ACCESS_KEY=your_r2_secret_key_here
R2_ACCOUNT_ID=your_cloudflare_account_id
```

### 2. Get Your Cloudflare R2 Credentials

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Navigate to **R2 Object Storage**
3. Go to **Manage R2 API tokens**
4. Create a new token or use existing one
5. Copy the **Access Key ID** and **Secret Access Key**
6. Your **Account ID** can be found in the right sidebar of your Cloudflare dashboard

### 3. Update Configuration

In the utility file, update the endpoint with your actual account ID:

```typescript
endpoint: 'https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com'
```

## 🎯 Basic Usage

### Import and Initialize

```typescript
import { createSparkStrandStorage } from './r2-storage-utility';

// Initialize the storage utility
const storage = createSparkStrandStorage();
```

### Upload Files

#### Upload from Buffer/File (Browser/Node.js)

```typescript
// Example: Upload an image
const imageBuffer = fs.readFileSync('path/to/image.jpg');

const imageUrl = await storage.uploadFile('images/logo.jpg', imageBuffer, {
  contentType: 'image/jpeg',
  cacheControl: 'public, max-age=86400' // Cache for 1 day
});

console.log('Image uploaded:', imageUrl);
// Output: https://storage.strandcdn.com/images/logo.jpg
```

#### Upload from File Path (Node.js only)

```typescript
// Upload directly from file system
const fileUrl = await storage.uploadFromPath(
  './local/file.pdf',
  'documents/important.pdf',
  {
    contentType: 'application/pdf',
    metadata: { 
      uploadedBy: 'user123',
      category: 'documents' 
    }
  }
);

console.log('File uploaded:', fileUrl);
```

#### Upload with Auto-Generated Unique Name

```typescript
// Generate unique filename to avoid conflicts
const uniqueKey = storage.generateUniqueKey('photo.jpg', 'uploads');
// Result: uploads/photo-1691234567890-abc123.jpg

const url = await storage.uploadFile(uniqueKey, fileBuffer);
```

### Download Files

```typescript
// Download a file from R2
const fileBuffer = await storage.getFile('images/logo.jpg');

// Save to local file system (Node.js)
fs.writeFileSync('./downloaded-logo.jpg', fileBuffer);
```

### Download from External URLs

```typescript
// Download from external URL and store in R2
const result = await storage.downloadAndStore(
  'https://example.com/image.jpg',
  'external-images/downloaded-image.jpg',
  {
    maxFileSize: 10 * 1024 * 1024, // 10MB limit
    timeout: 15000, // 15 seconds timeout
    cacheControl: 'public, max-age=86400',
    metadata: {
      source: 'external-download',
      category: 'user-content'
    }
  }
);

console.log('Downloaded and stored:', result);
/*
{
  url: 'https://storage.strandcdn.com/external-images/downloaded-image.jpg',
  key: 'external-images/downloaded-image.jpg',
  size: 245760,
  contentType: 'image/jpeg',
  originalUrl: 'https://example.com/image.jpg',
  filename: 'image.jpg'
}
*/

// Download with auto-generated unique key
const autoResult = await storage.downloadAndStoreWithAutoKey(
  'https://example.com/document.pdf',
  'downloads' // folder
);
// File will be stored with unique name like: downloads/document-1691234567890-abc123.pdf
```

### Batch Download from Multiple URLs

```typescript
// Download multiple files at once
const downloads = [
  {
    url: 'https://example.com/image1.jpg',
    folder: 'batch-images'
  },
  {
    url: 'https://example.com/document.pdf',
    key: 'documents/important.pdf',
    options: { 
      maxFileSize: 50 * 1024 * 1024 // 50MB for this file
    }
  },
  {
    url: 'https://api.example.com/user/avatar.png',
    folder: 'avatars'
  }
];

const results = await storage.batchDownloadAndStore(downloads);

results.forEach((result, index) => {
  if (result.success) {
    console.log(`✅ Downloaded: ${result.data?.url}`);
  } else {
    console.log(`❌ Failed: ${result.originalUrl} - ${result.error}`);
  }
});
```

### File Information

```typescript
// Get file metadata
const fileInfo = await storage.getFileInfo('images/logo.jpg');
console.log(fileInfo);
/*
{
  key: 'images/logo.jpg',
  size: 15420,
  lastModified: 2024-08-07T10:30:00.000Z,
  etag: '"d41d8cd98f00b204e9800998ecf8427e"',
  contentType: 'image/jpeg'
}
*/

// Check if file exists
const exists = await storage.fileExists('images/logo.jpg');
console.log('File exists:', exists); // true or false
```

### List Files

```typescript
// List all files
const allFiles = await storage.listFiles();

// List files in specific folder
const imageFiles = await storage.listFiles('images/');

// List with limit
const recentFiles = await storage.listFiles('uploads/', 50);

console.log('Files:', imageFiles);
/*
[
  {
    key: 'images/logo.jpg',
    size: 15420,
    lastModified: 2024-08-07T10:30:00.000Z,
    etag: '"abc123..."'
  },
  // ... more files
]
*/
```

### Delete Files

```typescript
// Delete a single file
await storage.deleteFile('images/old-logo.jpg');
console.log('File deleted successfully');
```

### Generate URLs

```typescript
// Get public URL (always accessible)
const publicUrl = storage.getPublicUrl('images/logo.jpg');
console.log(publicUrl);
// Output: https://storage.strandcdn.com/images/logo.jpg

// Generate temporary presigned URL (expires after specified time)
const tempUrl = await storage.getPresignedUrl('private/document.pdf', 3600); // 1 hour
console.log('Temporary URL:', tempUrl);
```

## 🎨 Advanced Examples

### Image Upload with Processing

```typescript
async function uploadUserAvatar(userId: string, imageFile: Buffer) {
  try {
    // Generate unique key for the avatar
    const avatarKey = storage.generateUniqueKey(`avatar.jpg`, `users/${userId}`);
    
    // Upload with appropriate settings for images
    const avatarUrl = await storage.uploadFile(avatarKey, imageFile, {
      contentType: 'image/jpeg',
      cacheControl: 'public, max-age=2592000', // 30 days
      metadata: {
        userId: userId,
        uploadType: 'avatar',
        timestamp: new Date().toISOString()
      }
    });
    
    return { success: true, url: avatarUrl, key: avatarKey };
  } catch (error) {
    console.error('Avatar upload failed:', error);
    return { success: false, error: error.message };
  }
}

// Usage
const result = await uploadUserAvatar('user123', avatarBuffer);
if (result.success) {
  console.log('Avatar uploaded:', result.url);
}
```

### Bulk File Operations

```typescript
async function uploadMultipleFiles(files: Array<{name: string, buffer: Buffer}>) {
  const results = [];
  
  for (const file of files) {
    try {
      const key = storage.generateUniqueKey(file.name, 'bulk-upload');
      const url = await storage.uploadFile(key, file.buffer);
      results.push({ name: file.name, url, success: true });
    } catch (error) {
      results.push({ name: file.name, error: error.message, success: false });
    }
  }
  
  return results;
}
```

### File Cleanup Utility

```typescript
async function cleanupOldFiles(folderPrefix: string, daysOld: number = 30) {
  const files = await storage.listFiles(folderPrefix);
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);
  
  const filesToDelete = files.filter(file => file.lastModified < cutoffDate);
  
  for (const file of filesToDelete) {
    try {
      await storage.deleteFile(file.key);
      console.log(`Deleted old file: ${file.key}`);
    } catch (error) {
      console.error(`Failed to delete ${file.key}:`, error);
    }
  }
  
  return filesToDelete.length;
}

// Clean up files older than 30 days
const deletedCount = await cleanupOldFiles('temp/', 30);
console.log(`Cleaned up ${deletedCount} old files`);
```

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit credentials to version control
2. **Access Control**: Use R2 bucket policies to restrict access
3. **Presigned URLs**: Use for temporary access instead of making files permanently public
4. **Validation**: Always validate file types and sizes before upload
5. **Error Handling**: Implement proper error handling for production use

## 📝 Common Use Cases

### Profile Picture Upload

```typescript
async function handleProfilePictureUpload(userId: string, file: File) {
  // Validate file type
  if (!file.type.startsWith('image/')) {
    throw new Error('Only image files are allowed');
  }
  
  // Convert File to Buffer (in Node.js environment)
  const buffer = Buffer.from(await file.arrayBuffer());
  
  // Upload with user-specific path
  const key = `profiles/${userId}/avatar.${file.type.split('/')[1]}`;
  
  return await storage.uploadFile(key, buffer, {
    contentType: file.type,
    cacheControl: 'public, max-age=86400'
  });
}
```

### External File Import

```typescript
async function importUserProfileImage(userId: string, imageUrl: string) {
  try {
    // Download external image and store in user's folder
    const result = await storage.downloadAndStore(
      imageUrl,
      `profiles/${userId}/avatar.jpg`,
      {
        maxFileSize: 5 * 1024 * 1024, // 5MB limit for avatars
        timeout: 10000, // 10 second timeout
        contentType: 'image/jpeg',
        cacheControl: 'public, max-age=2592000', // 30 days
        metadata: {
          userId: userId,
          importedFrom: imageUrl,
          type: 'profile-avatar'
        }
      }
    );

    // Update user's profile with new avatar URL
    return {
      success: true,
      avatarUrl: result.url,
      fileSize: result.size,
      originalSource: result.originalUrl
    };
    
  } catch (error) {
    console.error('Failed to import profile image:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
}

// Usage
const result = await importUserProfileImage('user123', 'https://example.com/user-avatar.jpg');
```

### Social Media Content Backup

```typescript
async function backupSocialMediaContent(posts: Array<{id: string, mediaUrl: string, type: string}>) {
  const downloads = posts.map(post => ({
    url: post.mediaUrl,
    folder: `social-backup/${post.type}`,
    options: {
      maxFileSize: 100 * 1024 * 1024, // 100MB
      metadata: {
        postId: post.id,
        contentType: post.type,
        backupDate: new Date().toISOString()
      }
    }
  }));

  const results = await storage.batchDownloadAndStore(downloads);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  return {
    totalProcessed: results.length,
    successful: successful.length,
    failed: failed.length,
    backupUrls: successful.map(r => r.data?.url),
    errors: failed.map(r => ({ url: r.originalUrl, error: r.error }))
  };
}
```

### Website Asset Mirror

```typescript
async function mirrorWebsiteAssets(assetUrls: string[]) {
  const downloads = assetUrls.map(url => ({
    url,
    folder: 'website-assets',
    options: {
      timeout: 30000, // 30 seconds for large assets
      followRedirects: true,
      cacheControl: 'public, max-age=31536000' // 1 year for static assets
    }
  }));

  return await storage.batchDownloadAndStore(downloads);
}

// Mirror CSS, JS, and image files
const assetUrls = [
  'https://example.com/styles/main.css',
  'https://example.com/js/app.js',
  'https://example.com/images/hero.jpg'
];

const mirrorResults = await mirrorWebsiteAssets(assetUrls);
```

## 🐛 Troubleshooting

### Common Errors

1. **"Access Denied"**: Check your R2 credentials and permissions
2. **"Bucket not found"**: Verify bucket name and region
3. **"Invalid endpoint"**: Make sure your account ID is correct in the endpoint URL
4. **"File not found"**: Check if the file key exists using `fileExists()`

### Debug Mode

Enable debug logging by setting environment variable:

```bash
export AWS_SDK_JS_LOG_LEVEL=debug
```

## 📊 API Reference

### Methods

| Method | Description | Parameters | Returns |
|--------|-------------|------------|---------|
| `uploadFile(key, file, options?)` | Upload a file | key: string, file: Buffer, options?: UploadOptions | Promise\<string\> |
| `uploadFromPath(path, key, options?)` | Upload from file path | path: string, key: string, options?: UploadOptions | Promise\<string\> |
| `getFile(key)` | Download a file | key: string | Promise\<Buffer\> |
| `getFileInfo(key)` | Get file metadata | key: string | Promise\<FileInfo\> |
| `deleteFile(key)` | Delete a file | key: string | Promise\<void\> |
| `listFiles(prefix?, maxKeys?)` | List files | prefix?: string, maxKeys?: number | Promise\<FileInfo[]\> |
| `fileExists(key)` | Check if file exists | key: string | Promise\<boolean\> |
| `getPresignedUrl(key, expiresIn?)` | Generate temporary URL | key: string, expiresIn?: number | Promise\<string\> |
| `getPublicUrl(key)` | Get public URL | key: string | string |
| `generateUniqueKey(name, folder?)` | Generate unique filename | name: string, folder?: string | string |

### Types

```typescript
interface UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  cacheControl?: string;
  expires?: Date;
}

interface DownloadOptions extends UploadOptions {
  followRedirects?: boolean;
  maxFileSize?: number; // bytes, default: 100MB
  timeout?: number; // milliseconds, default: 30000
}

interface DownloadResult {
  url: string;
  key: string;
  size: number;
  contentType: string;
  originalUrl: string;
  filename?: string;
}

interface FileInfo {
  key: string;
  size: number;
  lastModified: Date;
  etag: string;
  contentType?: string;
}
```


## Contributing

Feel free to submit issues and enhancement requests for this utility.