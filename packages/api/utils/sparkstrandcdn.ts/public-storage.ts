import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, ListObjectsV2Command, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export interface R2Config {
  bucketName: string;
  bucketUrl: string;
  accessKeyId: string;
  secretAccessKey: string;
  endpoint: string;
}

export interface UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  cacheControl?: string;
  expires?: Date;
}

export interface FileInfo {
  key: string;
  size: number;
  lastModified: Date;
  etag: string;
  contentType?: string;
}

export class R2StorageUtility {
  private s3Client: S3Client;
  private config: R2Config;

  constructor(config: R2Config) {
    this.config = config;
    this.s3Client = new S3Client({
      region: 'auto', // Cloudflare R2 uses 'auto' as region
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });
  }

  /**
   * Upload a file to R2 storage
   */
  async uploadFile(
    key: string,
    file: Buffer | Uint8Array | string,
    options: UploadOptions = {}
  ): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
        Body: file,
        ContentType: options.contentType || 'application/octet-stream',
        Metadata: options.metadata,
        CacheControl: options.cacheControl,
        Expires: options.expires,
      });

      await this.s3Client.send(command);
      return this.getPublicUrl(key);
    } catch (error) {
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload a file from a local path (Node.js environment)
   */
  async uploadFromPath(
    filePath: string,
    key: string,
    options: UploadOptions = {}
  ): Promise<string> {
    const fs = await import('fs').then(m => m.promises);
    const path = await import('path');
    
    try {
      const fileBuffer = await fs.readFile(filePath);
      
      // Auto-detect content type if not provided
      if (!options.contentType) {
        const ext = path.extname(filePath).toLowerCase();
        options.contentType = this.getContentTypeFromExtension(ext);
      }

      return await this.uploadFile(key, fileBuffer, options);
    } catch (error) {
      throw new Error(`Failed to upload file from path: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Retrieve a file from R2 storage
   */
  async getFile(key: string): Promise<Buffer> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      const response = await this.s3Client.send(command);
      
      if (!response.Body) {
        throw new Error('File body is empty');
      }

      const chunks: Uint8Array[] = [];
      const reader = response.Body.transformToWebStream().getReader();
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      return Buffer.concat(chunks);
    } catch (error) {
      throw new Error(`Failed to retrieve file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get file metadata without downloading the file
   */
  async getFileInfo(key: string): Promise<FileInfo> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      const response = await this.s3Client.send(command);

      return {
        key,
        size: response.ContentLength || 0,
        lastModified: response.LastModified || new Date(),
        etag: response.ETag || '',
        contentType: response.ContentType,
      };
    } catch (error) {
      throw new Error(`Failed to get file info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a file from R2 storage
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
    } catch (error) {
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * List files in the bucket with optional prefix
   */
  async listFiles(prefix?: string, maxKeys = 1000): Promise<FileInfo[]> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.config.bucketName,
        Prefix: prefix,
        MaxKeys: maxKeys,
      });

      const response = await this.s3Client.send(command);
      
      return (response.Contents || []).map(object => ({
        key: object.Key || '',
        size: object.Size || 0,
        lastModified: object.LastModified || new Date(),
        etag: object.ETag || '',
      }));
    } catch (error) {
      throw new Error(`Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if a file exists
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      await this.getFileInfo(key);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a presigned URL for temporary access
   */
  async getPresignedUrl(key: string, expiresIn = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      throw new Error(`Failed to generate presigned URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get the public URL for a file
   */
  getPublicUrl(key: string): string {
    return `${this.config.bucketUrl}/${key}`;
  }

  /**
   * Generate a unique key with timestamp
   */
  generateUniqueKey(originalName: string, folder?: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const extension = originalName.split('.').pop();
    const baseName = originalName.split('.').slice(0, -1).join('.');
    
    const fileName = `${baseName}-${timestamp}-${randomString}.${extension}`;
    return folder ? `${folder}/${fileName}` : fileName;
  }

  /**
   * Download file from external URL and store it in R2
   */
  async downloadAndStore(
    externalUrl: string,
    key: string,
    options: UploadOptions & { 
      followRedirects?: boolean;
      maxFileSize?: number;
      timeout?: number;
    } = {}
  ): Promise<{
    url: string;
    key: string;
    size: number;
    contentType: string;
    originalUrl: string;
    filename?: string;
  }> {
    const {
      followRedirects = true,
      maxFileSize = 100 * 1024 * 1024, // 100MB default limit
      timeout = 90000, // 90 seconds
      ...uploadOptions
    } = options;

    try {
      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      // Fetch the external file
      const response = await fetch(externalUrl, {
        signal: controller.signal,
        redirect: followRedirects ? 'follow' : 'manual',
        headers: {
          'User-Agent': 'SparkStrand-R2-Utility/1.0'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch external file: ${response.status} ${response.statusText}`);
      }

      // Check content length
      const contentLength = response.headers.get('content-length');
      if (contentLength && parseInt(contentLength) > maxFileSize) {
        throw new Error(`File too large: ${contentLength} bytes (max: ${maxFileSize} bytes)`);
      }

      // Get content type from response headers
      const responseContentType = response.headers.get('content-type') || 'application/octet-stream';
      
      // Extract filename from URL or Content-Disposition header
      let filename: string | undefined;
      const contentDisposition = response.headers.get('content-disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }
      
      if (!filename) {
        // Extract filename from URL
        const urlPath = new URL(externalUrl).pathname;
        filename = urlPath.split('/').pop() || 'downloaded-file';
      }

      // Convert response to buffer
      const arrayBuffer = await response.arrayBuffer();
      const fileBuffer = Buffer.from(arrayBuffer);

      // Check actual file size
      if (fileBuffer.length > maxFileSize) {
        throw new Error(`File too large: ${fileBuffer.length} bytes (max: ${maxFileSize} bytes)`);
      }

      // Use provided content type or detect from response/extension
      const finalContentType = uploadOptions.contentType || 
                              responseContentType || 
                              this.getContentTypeFromExtension(this.getFileExtension(filename));

      // Upload to R2
      const publicUrl = await this.uploadFile(key, fileBuffer, {
        ...uploadOptions,
        contentType: finalContentType,
        metadata: {
          ...uploadOptions.metadata,
          originalUrl: externalUrl,
          originalFilename: filename,
          downloadedAt: new Date().toISOString(),
        }
      });

      return {
        url: publicUrl,
        key,
        size: fileBuffer.length,
        contentType: finalContentType,
        originalUrl: externalUrl,
        filename
      };

    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error(`Download timeout after ${timeout}ms`);
      }
      throw new Error(`Failed to download and store file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Download file from external URL with auto-generated key
   */
  async downloadAndStoreWithAutoKey(
    externalUrl: string,
    folder?: string,
    options: Parameters<typeof this.downloadAndStore>[2] = {}
  ): Promise<ReturnType<typeof this.downloadAndStore>> {
    try {
      // Extract filename from URL for key generation
      const urlPath = new URL(externalUrl).pathname;
      let filename = urlPath.split('/').pop() || 'downloaded-file';
      
      // If no extension, try to get from content-type later
      if (!filename.includes('.')) {
        filename += '.bin';
      }

      // Generate unique key
      const uniqueKey = this.generateUniqueKey(filename, folder);
      
      return await this.downloadAndStore(externalUrl, uniqueKey, options);
    } catch (error) {
      throw new Error(`Failed to download with auto key: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Batch download multiple files from external URLs
   */
  async batchDownloadAndStore(
    downloads: Array<{
      url: string;
      key?: string;
      folder?: string;
      options?: Parameters<typeof this.downloadAndStore>[2];
    }>
  ): Promise<Array<{
    success: boolean;
    data?: Awaited<ReturnType<typeof this.downloadAndStore>>;
    error?: string;
    originalUrl: string;
  }>> {
    const results = await Promise.allSettled(
      downloads.map(async (download) => {
        if (download.key) {
          return await this.downloadAndStore(download.url, download.key, download.options);
        } else {
          return await this.downloadAndStoreWithAutoKey(download.url, download.folder, download.options);
        }
      })
    );

    return results.map((result, index) => ({
      originalUrl: downloads[index].url,
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : undefined,
      error: result.status === 'rejected' ? result.reason.message : undefined,
    }));
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
  }

  /**
   * Helper method to get content type from file extension
   */
  private getContentTypeFromExtension(extension: string): string {
    const contentTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.csv': 'text/csv',
      '.mp4': 'video/mp4',
      '.mp3': 'audio/mpeg',
      '.zip': 'application/zip',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };

    return contentTypes[extension] || 'application/octet-stream';
  }

  /**
   * Factory method to create R2StorageUtility instance
   */
  static createR2Storage(config?: R2Config): R2StorageUtility {
    const defaultConfig: R2Config = {
        bucketName: process.env.R2_BUCKET_NAME || '',
        bucketUrl: process.env.R2_BUCKET_URL || '',
        accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
        endpoint: process.env.R2_ENDPOINT || '',
    }
    if(!config){
      config = defaultConfig;
    }
    return new R2StorageUtility(config);
  }
}

export const storage = R2StorageUtility.createR2Storage();

