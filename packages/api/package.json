{"name": "@sparkstrand/chat-api", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"dev": "npx nodemon index.ts", "dev:fast": "NODE_ENV=development ts-node-dev --transpile-only --respawn --ignore-watch node_modules index.ts", "test": "npx jest  --maxWorkers=50%", "typecheck": "tsc --noEmit", "start": "node dist/index.js", "postinstall": "npx prisma generate", "build": "tsup", "client": "cd client && yarn start", "server": "npx nodemon index.ts", "compile": "tsc -b index.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@turbo/gen": "^1.12.4", "@types/bcrypt": "^5", "@types/body-parser": "^1.19.0", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.2", "@types/express": "^5.0.0", "@types/hpp": "^0.2.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^8.5.0", "@types/mongoose": "^5.10.3", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.5", "@types/multer-s3": "3.0.3", "@types/node": "^20.11.24", "@types/react": "18.3.0", "@types/react-dom": "18.3.1", "@types/socket.io": "^2.1.12", "@types/swagger-ui-express": "^4.1.8", "@types/winston": "^2.4.4", "@types/yamljs": "^0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "nodemon": "^2.0.6", "ts-auto-mock": "^3.6.2", "ts-jest": "^29.2.5", "ts-node-dev": "^2.0.0", "tsup": "^8.0.2", "typescript": "5.5.4"}, "dependencies": {"@aws-sdk/client-s3": "3.735.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@azure/storage-blob": "^12.27.0", "@emotion/babel-plugin": "^11.1.2", "@emotion/core": "^11.0.0", "@google-cloud/storage": "^7.16.0", "@prisma/client": "^6.6.0", "autoprefixer": "^9", "awilix": "12.0.5", "axios": "^0.21.0", "bcrypt": "^5.1.1", "compression": "^1.7.5", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "date-fns": "^2.16.1", "dotenv": "^8.2.0", "express": "4.21.2", "express-async-errors": "3.1.1", "express-rate-limit": "7.5.0", "helmet": "8.0.0", "hpp": "^0.2.3", "jsonwebtoken": "^8.5.1", "mongoose": "^5.10.18", "morgan": "^1.10.0", "multer": "^1.4.2", "multer-s3": "3.0.1", "socket.io": "4.8.1", "socketio-jwt": "^4.6.2", "swagger-ui-express": "^5.0.1", "ts-node": "^9.1.1", "ua-parser-js": "2.0.0", "uuid": "11.0.5", "winston": "^3.17.0", "yamljs": "^0.3.0", "zod": "^3.24.1"}, "engines": {"node": "22.10.0"}}