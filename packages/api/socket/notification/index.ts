import { Namespace } from "socket.io";
import { IGlobalMiddlewareSocket } from "../globalSecurity";

/**
 * Setup for the notification namespace.
 * Handles all Sparkstrand real-time in-app notification scenarios.
 * This will serve as proxy layer for the notification-react/in_app
 * and notification-react-native/in_app components in the Sparkstrand notification frontend libraries.
 */
export const setupNotificationApp = (notification: Namespace) => {
  console.log('Setting up notification namespace');
  
  // Custom middleware for notification namespace
  notification.use(async (socket: IGlobalMiddlewareSocket, next) => {
    try {
      //TODO: 
      //  Authenticated with https://notifications.sparkstrand.com/api/user
      //  Get user data from response and set on socket
      
      const user = socket.handshake.query?.user as string;
      const userId = socket.handshake.query?.userId as string;
      
      // Simple and flexible user identification for now.
      const userIdentifier = user || userId;

      if (!userIdentifier) {
        console.log('No user identifier provided for notification namespace');
        return next(new Error('Authentication failed: No user identifier'));
      }
      
      // Parse user data if it's a JSON string
      let userData;
      try {
        userData = typeof userIdentifier === 'string' ? JSON.parse(userIdentifier) : userIdentifier;
      } catch (e) {
        // If not JSON, treat as simple user ID
        userData = { id: userIdentifier };
      }
      
      socket.user = userData;
      console.log('User authenticated for notification namespace:', userData);
      next();
    } catch (error) {
      console.error('Error in notification middleware:', error);
      next(new Error('Authentication failed'));
    }
  });

  notification.on('connection', (socket: IGlobalMiddlewareSocket) => {
    console.log('User connected to notification namespace:', socket.user);
    
    // Join the socket to the user's room for targeted notifications
    const userRoom = socket.user?.id || socket.user;
    socket.join(userRoom.toString());
    
    console.log(`Socket ${socket.id} joined room: ${userRoom}`);
    
    // Send connection confirmation
    socket.emit('notification:connected', { 
      success: true, 
      room: userRoom
    });
    
    // Handle connection check
    socket.on("isConnected", () => {
      console.log('User checking connection status:', socket.user);
      socket.emit('notification:connected', { 
        success: true, 
        room: userRoom
      });
    });
    
    // Handle custom events for testing - by me
    socket.on('test:notification', (data) => {
      console.log('Test notification received:', data);
      socket.emit('test:response', { success: true, received: data });
    });
    
    // Handle disconnect
    socket.on('disconnect', (reason) => {
      console.log(`User disconnected from notification namespace. Reason: ${reason}`, socket.user);
      
      // Leave the user's room
      socket.leave(userRoom.toString());
      
      // Emit disconnect confirmation to any remaining sockets in the room
      // socket.to(userRoom.toString()).emit('notification:user_disconnected', { 
      //   success: true, 
      //   userId: userRoom,
      //   reason 
      // });
    });
  });
  
  // Handle namespace-level errors
  notification.on('connect_error', (err) => {
    console.log('Notification namespace connection error:', err);
  });
};