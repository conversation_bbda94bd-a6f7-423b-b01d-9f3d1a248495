import { ExtendedError } from "socket.io/dist/namespace";
import { AuthService } from "../../utils/auth";
import { ApiError } from "../../utils";
import { serviceLocator } from "../../utils/serviceLocator";
import { SocketLog } from "../utils";
import { IGlobalMiddlewareSocket } from "./types";
import { SocketEvent } from "../types/server.events";
import { GuestStatus } from "../general/general.types";



/**
 * Enhanced Socket.IO authentication middleware with additional security features
 */
export async function GlobalMiddleware(
  socket: IGlobalMiddlewareSocket,
  next: (err?: ExtendedError) => void
): Promise<void> {
  try {
    // Skip global middleware for notification namespace
    if (socket.nsp.name === '/notification') {
      console.log("Skipping global middleware for notification namespace");
      return next(); // Skip global middleware for notification namespace
    }
    const connectionTime = new Date();
    const ipAddress = socket.handshake.address;
    const userAgent = socket.handshake.headers['user-agent'];

    // Initialize rate limiting
    socket.rateLimits = {
      messages: {
        count: 0,
        lastReset: new Date(),
      },
      rooms: {
        count: 0,
        lastReset: new Date(),
      },
    };

    // Extract token from various sources
    const token =
      socket.handshake.headers.cookie?.split(';').find(c => c.trim().startsWith('sparkstrand_token=')).split('=')[1] ||
      socket.handshake.headers.cookie?.split(';').find(c => c.trim().startsWith('token=')).split('=')[1] ||
      socket.handshake.auth?.token ||
      socket.handshake.headers?.authorization?.split(' ')[1] ||
      socket.handshake.query?.token as string;

    if (!token) {
      SocketLog.warn(`Authentication failed: No token provided from IP ${ipAddress}`);
      return next(new ApiError('Unauthorized: No token provided', 401, 'Socket authentication middleware'));
    }

    // Verify token
    let payload;
    try {
      const decoded = AuthService.verifyToken(token);
      payload = decoded.data;
    } catch (error) {
      SocketLog.warn(`Authentication failed: Invalid token from IP ${ipAddress}`);
      return next(new ApiError('Unauthorized: Invalid token', 401, 'Socket authentication middleware'));
    }

    if (!payload || !payload.isAuthenticated) {
      SocketLog.warn(`Authentication failed: Token not authenticated from IP ${ipAddress}`);
      return next(new ApiError('Unauthorized: Token not authenticated', 401, 'Socket authentication middleware'));
    }

    // Retrieve user data based on type
    let userData;
    const guestService = await serviceLocator.getGuestService();

    if (payload.type === 'guest') {
      try {
        userData = await guestService.getGuestByIdAndCompanyId(payload.id, payload.companyId);
      } catch (error) {
        SocketLog.error(`Error retrieving guest data: ${error.message}`);
        return next(new ApiError('Internal server error', 500, 'Socket authentication middleware'));
      }
    } else if (payload.type === 'anonymous') {
      // Handle anonymous users
      // Not implemented yet
    } else if (payload.type === 'user' || payload.type === 'agent') {
      // Handle regular users and agents
      // Not implemented yet
    } else {
      SocketLog.warn(`Authentication failed: Invalid user type ${payload.type} from IP ${ipAddress}`);
      return next(new ApiError('Unauthorized: Invalid user type', 401, 'Socket authentication middleware'));
    }

    if (!userData.success) {
      SocketLog.warn(`Authentication failed: User not found for ID ${payload.id} from IP ${ipAddress}`);
      return next(new ApiError('Not found: User not found', 404, 'Socket authentication middleware'));
    }

    // Set user data on socket
    userData = userData.data;
    socket.user = {
      id: userData.id,
      type: payload.type,
      username: payload.type === 'guest' ? userData.username : undefined,
      companyId: payload.type === 'guest' ? payload.companyId : undefined,
      currentRoomId: userData?.currentRoomId || null,
      lastActivity: new Date(),
      connectionTime,
      ipAddress,
      userAgent,
    };

    SocketLog.info(`User authenticated: ${socket.user.id} (${socket.user.type}) from IP ${ipAddress}`);

    // Set up disconnect handler to update last activity
    socket.on(SocketEvent.DISCONNECT, async (reason) => {
      try {
        SocketLog.info(`User disconnected: ${socket.user.id} (${socket.user.type}), reason: ${reason}`);

        // Update last seen timestamp
        if (socket.user?.type === 'guest') {
          const guestService = await serviceLocator.getGuestService();
          await guestService.updateGuest(socket.user.id, { lastSeenAt: new Date(), status: GuestStatus.Offline });
        }
      } catch (error) {
        SocketLog.error(`Error updating last seen timestamp: ${error.message}`);
      }
    });

    return next();
  } catch (error) {
    SocketLog.error(`Socket authentication error: ${error.message}`, error);
    return next(new ApiError('Internal server error', 500, 'Socket authentication middleware'));
  }
}

