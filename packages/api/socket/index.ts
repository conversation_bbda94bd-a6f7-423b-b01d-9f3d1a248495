// socket/index.ts - Fixed version
import { Server } from "socket.io";
import { setupCustomerSupportApp } from "./customer-support";
import { setupTeamCollaborationApp } from "./team-collaboration";
import { setupCommunityApp } from "./community-chat";
import { setupEducationalApp } from "./educational-chat";
import { setupHealthcareTeleconsultationApp } from "./healthcare-teleconsultation";
import { setupEcommerceLiveShoppingApp } from "./ecommerce-live-shopping";
import { GlobalMiddleware } from "./globalSecurity";
import { setupGeneralApp } from "./general";
import { SocketEvent } from "./types/server.events";
import { SocketLog as logger } from "./utils";
import { setupNotificationApp } from "./notification";

/**
 * Enhanced Socket.IO setup with improved middleware and error handling
 */
export const setupSocket = (io: Server) => {
  logger.info('Setting up enhanced Socket.IO server');
  
  // Apply global authentication middleware but EXCLUDE the notification namespace
  io.use(GlobalMiddleware);
  
  // Set up error handling for the server
  io.engine.on(SocketEvent.CONNECT_ERROR, (err) => {
    logger.error(`Connection error: ${err.message}`, err);
  });
  
  // Setup each namespace with its own handlers
  logger.info('Setting up application namespaces');
  
  // Customer Support namespace
  setupCustomerSupportApp(io.of('/support'));
  
  // Team Collaboration namespace
  setupTeamCollaborationApp(io.of('/team'));
  
  // Community namespace
  setupCommunityApp(io.of('/community'));
  
  // Educational namespace
  setupEducationalApp(io.of('/education'));
  
  // Healthcare Teleconsultation namespace
  setupHealthcareTeleconsultationApp(io.of('/healthcare'));
  
  // Ecommerce Live Shopping namespace
  setupEcommerceLiveShoppingApp(io.of('/ecommerce'));
  
  // Notification namespace - Setup AFTER global middleware exclusion
  setupNotificationApp(io.of('/notification'));

  // General namespace (default)
  setupGeneralApp(io);
  
  logger.info('Socket.IO server setup complete');
  
  // Monitor active connections
  setInterval(() => {
    const counts = {
      total: io.engine.clientsCount,
      general: io.engine.clientsCount - 
        io.of('/support').sockets.size -
        io.of('/team').sockets.size -
        io.of('/community').sockets.size -
        io.of('/education').sockets.size -
        io.of('/healthcare').sockets.size -
        io.of('/ecommerce').sockets.size -
        io.of('/notification').sockets.size, // Include notification namespace
      support: io.of('/support').sockets.size,
      team: io.of('/team').sockets.size,
      community: io.of('/community').sockets.size,
      education: io.of('/education').sockets.size,
      healthcare: io.of('/healthcare').sockets.size,
      ecommerce: io.of('/ecommerce').sockets.size,
      notification: io.of('/notification').sockets.size 
    };
    
    logger.info(`Active connections: ${JSON.stringify(counts)}`);
  }, 60000);
};