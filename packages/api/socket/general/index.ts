import { Server } from "socket.io";
import { prisma } from "../../utils/prismaClient";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { GeneralAppMiddleware } from "./generalApp.middleware";
import { <PERSON><PERSON><PERSON><PERSON> } from "./room.handler";
import { <PERSON>Handler } from "./message.handler";
import { StatusHandler } from "./status.handler";
import { serviceLocator } from "../../utils/serviceLocator";
//import { SocketLog as logger } from "../utils";
import {  GuestStatus } from "./general.types";
import { SocketEvent } from "../types/server.events";
import { ICreateMessage, ICreateRoom } from "../../models";


export const setupGeneralApp = (generalServer: Server) => {
  generalServer.use(GeneralAppMiddleware);

  generalServer.on(SocketEvent.CONNECTION, async (socket: IGlobalMiddlewareSocket) => {
   console.log(`New connection established with ID: ${socket.id} for user ${socket.user?.id} (${socket.user?.type})`);

    if (!socket.user) {
      console.log(`No user data found for socket ${socket.id}, disconnecting after delay to prevent rapid reconnection`);
      // Add delay before disconnecting to prevent rapid reconnection attempts
      setTimeout(() => {
        socket.disconnect();
      }, 2000);
      return;
    }
    const roomService = await serviceLocator.getRoomService();
    const guestService = await serviceLocator.getGuestService();

    try {
      // const roomService = await serviceLocator.getRoomService();
      const guestRoomData = await roomService.getRoomsByGuestIdAndAppIdAndCompanyId(
        socket.user.id,
        socket.app.id,
        socket.user.companyId
      );

      await RoomHandler.JoinGuestToExistingRoomsOnLogin(socket.user.id, guestRoomData.data || [], socket, generalServer);

      socket.emit(SocketEvent.AUTHENTICATED, {
        user: {
          id: socket.user.id,
          type: socket.user.type,
          username: socket.user.username,
          currentRoomId: socket.user.currentRoomId
        },
        status: "Connected",
        applicationName: socket.app.name || "General App instance",
      });

     console.log(`User ${socket.user.id} (${socket.user.type}) authenticated successfully`);


      /************** ROOM MANAGEMENT SECTION ***********************/

      socket.on(SocketEvent.GET_LIST_OF_GUEST_ROOMS, async () => {
       console.log(`GET_LIST_OF_GUEST_ROOMS Event RECIEVED`);
        await RoomHandler.getListOfGuestRooms(socket.user.id, socket);
      });

      socket.on(SocketEvent.GET_ROOM_DATA_BY_ID, async (data: {roomId: string}) => {
       console.log(`GET_ROOM_DATA_BY_ID Event RECIEVED,`, data);
        await RoomHandler.getRoomDataById(socket.user.id, data.roomId, socket);
      });

      socket.on(SocketEvent.JOIN_ROOM, async (data: {roomId: string}) => {
        // const currentRoomId = socket.user.currentRoomId;
        // if (currentRoomId && currentRoomId !== roomId) {
        //   await roomService.updateOnlineMembersCount(currentRoomId, -1);
        // }
       console.log(`JOIN_ROOM Event RECIEVED,`, data);
        await RoomHandler.joinRoom(socket.user.id, data.roomId, socket, generalServer);
      });

      socket.on(SocketEvent.SWITCH_ROOM, async (data: {roomId: string}) => {
        console.log(`SWITCH_ROOM Event RECIEVED,`, data);
        await RoomHandler.switchRoom(socket.user.id, data.roomId, socket, generalServer);
      });

      socket.on(SocketEvent.REMOVE_USER_FROM_GROUP, async (data: {roomId: string, userId: string}) => {
       console.log(`User ${socket.user.id} attempted to remove user ${data.userId} from room ${data.roomId}`);
        // Implement permission checks and removal logic as needed
      });

      socket.on(SocketEvent.LEAVE_ROOM, async (data: {roomId: string}) => {
       console.log(`LEAVE_ROOM Event RECIEVED,`, data);
        await RoomHandler.leaveRoom(socket.user.id, data.roomId, socket, generalServer);
      });

      socket.on(SocketEvent.CREATE_ROOM, async (data: Omit<ICreateRoom, 'applicationId, creatorId'>) => {
       console.log(`CREATE_ROOM Event RECIEVED,`, data);
        await RoomHandler.createRoom(socket, generalServer, { ...data, applicationId: socket.app.id });
      });

      socket.on(SocketEvent.GET_ROOM_Media, async (data: { roomId: string}) => {
       console.log(`GET_ROOM_Media Event RECIEVED,`, data);
        const result = await RoomHandler.getRoomMedia (data.roomId, socket);
      });

      socket.on(SocketEvent.GET_ROOM_MESSAGES, async (data: { roomId: string, limit?: number, cursor?: string }) => {
       console.log(`GET_ROOM_MESSAGES Event RECIEVED,`, data);
        const result = await RoomHandler.getRoomMessages(data.roomId, socket, data.limit, data.cursor);
      });


      /************** MESSAGE MANAGEMENT SECTION ***********************/
      socket.on(SocketEvent.SEND_MESSAGE, async (data: Omit<ICreateMessage, 'senderId'>) => {
       console.log(`SEND_MESSAGE Event RECIEVED,`, data);
        await MessageHandler.sendMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.EDIT_MESSAGE, async (data: { messageId: string; text: string; roomId: string }) => {
       console.log(`EDIT_MESSAGE Event RECIEVED,`, data);
        await MessageHandler.editMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.DELETE_MESSAGE, async (data: { messageId: string; roomId: string }) => {
       console.log(`DELETE_MESSAGE Event RECIEVED,`, data);
        await MessageHandler.deleteMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.DELETE_MESSAGE, async (data: { messageId: string; roomId: string }) => {
       console.log(`DELETE_MESSAGE Event RECIEVED,`, data);
        await MessageHandler.deleteMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.MARK_MESSAGE_READ, async (data: {messageIds: string[]}) => {
       console.log(`MARK_MESSAGE_READ Event RECIEVED,`, data);
        await MessageHandler.markMessageRead(socket, generalServer, data);
      });

      socket.on(SocketEvent.SET_USER_STATUS, async (data: {status: GuestStatus}) => {
       console.log(`SET_USER_STATUS Event RECIEVED,`, data);
        await StatusHandler.setUserStatus(socket, generalServer, data.status);
      });

      socket.on(SocketEvent.TYPING, (data: { roomId: string }) => {
       console.log(`TYPING Event RECIEVED,`, data);
        StatusHandler.handleTyping(socket, generalServer, data.roomId);
      });

      socket.on(SocketEvent.STOP_TYPING, (data: { roomId: string }) => {
       console.log(`STOP_TYPING Event RECIEVED,`, data);
        StatusHandler.handleStopTyping(socket, generalServer, data.roomId);
      });

      socket.on(SocketEvent.DISCONNECT, async (reason) => {
       console.log(`User ${socket.user.id} (${socket.user.type}) disconnected: ${reason}`);

        try {
          let lastSeenAt: Date | undefined;
          if (socket.user.type === 'guest') {
            const updateData = await prisma.guest.update({
              where: { id: socket.user.id },
              data: { lastSeenAt: new Date(), status: GuestStatus.Offline }
            });
            if(socket.user.currentRoomId) {
              const { data: lastMessage } = await roomService.getRoomLastMessage(socket.user.currentRoomId);
              await guestService.guestRoomActivity(socket.user.id, socket.user.currentRoomId, lastMessage?.id);
            }

            lastSeenAt = updateData.lastSeenAt;
          } else if (socket.user.type === 'anonymous') {
            const updateData = await prisma.anonymous.update({
              where: { id: socket.user.id },
              data: { lastSeenAt: new Date() }
            });
            lastSeenAt = updateData.lastSeenAt;
          }


          socket.emit(SocketEvent.DISCONNECTED, {
            message: "Disconnected from server",
            lastSeenAt: lastSeenAt || new Date(),
            success: true,
            statusCode: 200
          });
          console.log(`User ${socket.user.id} (${socket.user.type}) disconnected: ${reason}`);

          if (socket.user.currentRoomId) {
            await roomService.updateOnlineMembersCount(socket.user.currentRoomId, -1);
            generalServer.to(socket.user.currentRoomId).emit(SocketEvent.USER_LEFT, {
              userId: socket.user.id,
              username: socket.user.username,
              userType: socket.user.type,
              roomId: socket.user.currentRoomId,
              reason: "disconnected",
              timestamp: new Date()
            });
          }
        } catch (error: any) {
          console.log(`Error handling disconnect: ${error.message}`, error);
        }
      });
    } catch (error: any) {
      console.log(`Error in connection handler: ${error.message}`, error);
      socket.emit(SocketEvent.ERROR, {
        message: "Internal server error",
        success: false,
        statusCode: 500
      });
    }
  });
};
