import { Server } from "socket.io";
import { IGlobalMiddlewareSocket, checkRateLimit } from "../globalSecurity";
import { <PERSON>rror<PERSON><PERSON><PERSON> } from "./error.handler";
import { IResponse  } from "./general.types";
import { typingManager } from "./typing.manager";
import { SocketLog as logger, getServices } from "../utils";
import { SocketEvent } from "../types/server.events";
import { ICreateMessage, IMessage } from "../../models";


export class MessageHandler {
  public static async sendMessage(
    socket: IGlobalMiddlewareSocket,
    io: Server,
    data: Omit<ICreateMessage, 'senderId'>
  ): Promise<IResponse<IMessage | null>> {
    try {
      const { messageService } = await getServices();
      if (!checkRateLimit(socket, 'messages')) {
        return ErrorHandler.handle(socket, new Error("Rate limit exceeded"), "Send message", "Rate limit exceeded");
      }

      if (!data.text && !data.files?.length) {
        return ErrorHandler.handle(socket, new Error("Message must contain text or files"), "Send message", "Message must contain text or files");
      }

      const sanitizedText = data.text ? data.text.trim().substring(0, 5000) : undefined;

      const message = await messageService.createMessage({...data, text: sanitizedText, senderId: socket.user.id });
      if(!message.success) {
        return ErrorHandler.handle(socket, new Error(message.message), "Send message", message.message);
      }

      socket.user.lastActivity = new Date();
      typingManager.handleStopTyping(socket, io, data.to);

      io.to(data.to).emit(SocketEvent.NEW_MESSAGE, message.data);

      logger.info(`Message sent by user ${socket.user.id} (${socket.user.type}) in room ${data.to}`);

      return message;
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Send message", "Failed to send message");
    }
  }

  public static async markMessageRead(
    socket: IGlobalMiddlewareSocket,
    io: Server,
    data: {
      messageIds: string[],
    }
  ): Promise<void> {
    try {

      if (!data.messageIds || !data.messageIds.length) {
        return;
      }
      const { messageService } = await getServices();
      const message = await messageService.markMessageRead(data.messageIds);

      logger.debug(`Message ${data.messageIds} marked as read by user ${socket.user.id}`);
    } catch (error: any) {
      ErrorHandler.handle(socket, error, "Mark message read", "Failed to mark message as read");
    }
  }

  public static async editMessage(
    socket: IGlobalMiddlewareSocket,
    io: Server,
    data: {
      messageId: string;
      text: string;
      roomId: string;
    }
  ): Promise<IResponse<IMessage | null>> {
    try {
      const { messageService } = await getServices();
      const message = await messageService.editMessageText(data.messageId, data.text, socket.user.id);

      if(!message.success) {
        return ErrorHandler.handle(socket, new Error(message.message), "Edit message", message.message);
      }

      io.to(data.roomId).emit(SocketEvent.MESSAGE_EDITED, { data: message.data });

      logger.debug(`Message ${data.messageId} edited by user ${socket.user.id}`);
      return message;
    } catch (error: any) {
      ErrorHandler.handle(socket, error, "Edit message", "Failed to edit message");
    }
  }

  public static async deleteMessage(
    socket: IGlobalMiddlewareSocket,
    io: Server,
    data: {
      messageId: string;
      roomId: string;
    }
  ): Promise<IResponse<IMessage | null>> {
    try {
      const { messageService } = await getServices();
      const message = await messageService.deleteMessage(data.messageId, socket.user.id);

      if(!message.success) {
        return ErrorHandler.handle(socket, new Error(message.message), "Delete message", message.message);
      }

      io.to(data.roomId).emit(SocketEvent.MESSAGE_DELETED, { data: message.data });

      logger.debug(`Message ${data.messageId} deleted by user ${socket.user.id}`);
    } catch (error: any) {
      ErrorHandler.handle(socket, error, "Delete message", "Failed to delete message");
    }
  }

}

