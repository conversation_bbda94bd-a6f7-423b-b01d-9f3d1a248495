import { Server } from "socket.io";
import { IGlobalMiddlewareSocket, checkRateLimit } from "../globalSecurity";
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from "./error.handler";
import { IResponse, RoomType, GuestStatus } from "./general.types";
import { getServices } from "../utils";
import { SocketEvent } from "../types/server.events";
import { ICreateRoom, IRoom, IRoomMedia } from "../../models";

export class RoomHandler {
  /**
   * Emits an event to a socket with logging
   */
  private static emitToSocket<T = any>(
    socket: IGlobalMiddlewareSocket, 
    event: SocketEvent, 
    data: T, 
    context: string
  ): void {
    socket.emit(event, data);
    console.log(`Emitted ${event} to socket ${socket.id} in ${context}`, { 
      socketId: socket.id, 
      userId: socket.user?.id, 
      event, 
      dataKeys: typeof data === 'object' && data ? Object.keys(data) : 'primitive',
      context 
    });
  }

  /**
   * Emits an event to a room with logging
   */
  private static emitToRoom<T = any>(
    io: Server, 
    roomId: string | string[], 
    event: SocketEvent, 
    data: T, 
    context: string
  ): void {
    io.to(roomId).emit(event, data);
    const roomIds = Array.isArray(roomId) ? roomId : [roomId];
    console.log(`Emitted ${event} to room(s) ${roomIds.join(', ')} in ${context}`, { 
      roomIds, 
      event, 
      dataKeys: typeof data === 'object' && data ? Object.keys(data) : 'primitive',
      context 
    });
  }

  /**
   * Handles guest joining existing rooms on login
   */
  public static async JoinGuestToExistingRoomsOnLogin(
    id: string,
    listOfRooms: IRoom[],
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<IRoom[] | null>> {
    const context = "JoinGuestToExistingRoomsOnLogin";
    
    try {
      console.log(`Starting ${context} for guest ${id} with ${listOfRooms?.length || 0} rooms`);
      
      const { roomService, guestService } = await getServices();
      const roomIds = listOfRooms?.map(room => room.id) || [];

      // Join socket to rooms
      socket.join(roomIds);
      console.log(`Socket ${socket.id} joined ${roomIds.length} rooms: ${roomIds.join(', ')}`);

      // Emit list of rooms to guest
      this.emitToSocket(socket, SocketEvent.LIST_OF_GUEST_ROOMS, listOfRooms, context);

      // Update user activity and notify other users
      socket.user.lastActivity = new Date();
      const userOnlineData = {
        userId: id,
        username: socket.user.username,
        userType: socket.user.type,
        roomId: roomIds,
        timestamp: new Date()
      };
      this.emitToRoom(io, roomIds, SocketEvent.USER_ONLINE, userOnlineData, context);

      // Update guest status and room membership
      const { data: guest } = await guestService.getGuestById(id);
      await Promise.allSettled(
        roomIds.map(roomId => roomService.updateOnlineMembersCount(roomId, 1))
      );

      if (!guest.currentRoomId) {
        await guestService.updateGuest(id, { 
          currentRoomId: roomIds[0], 
          status: GuestStatus.Online 
        });
        console.log(`Guest ${id} current room set to ${roomIds[0]}`);
      } else {
        await guestService.updateGuest(id, { status: GuestStatus.Online });
        console.log(`Guest ${id} status updated to Online`);
      }

      console.log(`${context} completed successfully for guest ${id}`);
      return { 
        success: true, 
        message: "Guest joined rooms successfully", 
        statusCode: 200, 
        data: listOfRooms 
      };
    } catch (error: any) {
      console.log(`Error in ${context} for guest ${id}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Join room", "Failed to join room");
    }
  }

  /**
   * Gets list of rooms for a guest
   */
  public static async getListOfGuestRooms(
    id: string,
    socket: IGlobalMiddlewareSocket,
  ): Promise<IResponse<IRoom[] | null>> {
    const context = "getListOfGuestRooms";
    
    try {
      console.log(`Getting room list for guest ${id}`);
      
      const { roomService } = await getServices();
      const guestRoomData = await roomService.getRoomsByGuestId(id);

      this.emitToSocket(
        socket, 
        SocketEvent.LIST_OF_GUEST_ROOMS, 
        guestRoomData?.data || [], 
        context
      );

      console.log(`Retrieved ${guestRoomData?.data?.length || 0} rooms for guest ${id}`);
      return guestRoomData;
    } catch (error: any) {
      console.log(`Error in ${context} for guest ${id}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Get list of guest rooms", "Failed to get list of guest rooms");
    }
  }

  /**
   * Gets room data by ID for authorized users
   */
  public static async getRoomDataById(
    id: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
  ): Promise<IResponse<IRoom | null>> {
    const context = "getRoomDataById";
    
    try {
      console.log(`Getting room data for room ${roomId} by user ${id}`);
      
      const { roomService } = await getServices();
      const isMember = await roomService.isGuestMemberOfRoom(id, roomId);
      
      if (!isMember) {
        const errorResponse = {
          success: false,
          message: "User is not a member of this room",
          statusCode: 403
        };
        
        this.emitToSocket(socket, SocketEvent.ERROR, errorResponse, context);
        console.log(`Access denied: User ${id} is not a member of room ${roomId}`);
        
        return { ...errorResponse, data: null };
      }

      const roomData = await roomService.getRoomFullDetails(roomId);
      
      if (!roomData.success) {
        this.emitToSocket(socket, SocketEvent.ERROR, roomData, context);
        console.log(`Failed to get room data for room ${roomId}: ${roomData.message}`);
        return roomData;
      }

      this.emitToSocket(socket, SocketEvent.ROOM_DATA, roomData.data, context);
      console.log(`Successfully retrieved room data for room ${roomId}`);
      
      return roomData;
    } catch (error: any) {
      console.log(`Error in ${context} for room ${roomId} by user ${id}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Get room data by id", "Failed to get room data");
    }
  }

  /**
   * Switches user to a different room
   */
  public static async switchRoom(
    id: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<IRoom | null>> {
    const context = "switchRoom";
    
    try {
      console.log(`User ${id} attempting to switch to room ${roomId}`);
      
      const { roomService, guestService } = await getServices();
      const isMember = await roomService.isGuestMemberOfRoom(id, roomId);

      if (!isMember) {
        const errorResponse = {
          success: false,
          message: "User is not a member of this room",
          statusCode: 403
        };
        
        this.emitToSocket(socket, SocketEvent.ERROR, errorResponse, context);
        console.log(`Switch room denied: User ${id} is not a member of room ${roomId}`);
        
        return { ...errorResponse, data: null };
      }

      // Handle leaving previous room
      if (socket.user.currentRoomId && socket.user.currentRoomId !== roomId) {
        const previousRoomId = socket.user.currentRoomId;
        
        const { data: lastMessage } = await roomService.getRoomLastMessage(previousRoomId);
        await guestService.guestRoomActivity(id, previousRoomId, lastMessage?.id);
        await socket.leave(previousRoomId);
        
        console.log(`User ${id} left previous room ${previousRoomId} when switching to ${roomId}`);
      }

      // Join new room
      await socket.join(roomId);
      socket.user.currentRoomId = roomId;
      socket.user.lastActivity = new Date();

      // Emit room switched event
      this.emitToSocket(socket, SocketEvent.ROOM_SWITCHED, { roomId }, context);

      // Fetch and send room data
      const roomDataResponse = await this.getRoomDataById(id, roomId, socket);

      console.log(`User ${id} successfully switched to room ${roomId}`);
      
      return {
        success: true,
        message: "Room switched successfully",
        statusCode: 200,
        data: roomDataResponse.data
      };
    } catch (error: any) {
      console.log(`Error in ${context} for user ${id} switching to room ${roomId}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Switch room", "Failed to switch room");
    }
  }

  /**
   * Adds user to a room
   */
  public static async joinRoom(
    id: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<number>> {
    const context = "joinRoom";
    
    try {
      console.log(`User ${id} attempting to join room ${roomId}`);
      
      const { roomService, guestService } = await getServices();
      
      if (!checkRateLimit(socket, 'rooms')) {
        console.log(`Rate limit exceeded for user ${id} joining room ${roomId}`);
        return ErrorHandler.handle(socket, new Error("Rate limit exceeded"), "Join room", "Rate limit exceeded");
      }

      let roomData: IResponse<number>;
      
      if (socket.user.type === 'guest') {
        roomData = await roomService.addGuestToRoom(id, roomId);
      } else {
        roomData = { 
          success: false, 
          message: "User type not supported", 
          statusCode: 400, 
          data: null 
        };
      }

      if (!roomData.success) {
        this.emitToSocket(socket, SocketEvent.ERROR, roomData, context);
        console.log(`Failed to add user ${id} to room ${roomId}: ${roomData.message}`);
        return roomData;
      }

      // Handle previous room activity tracking
      if (socket.user.currentRoomId && socket.user.currentRoomId !== roomId) {
        const { data: lastMessage } = await roomService.getRoomLastMessage(socket.user.currentRoomId);
        await guestService.guestRoomActivity(id, socket.user.currentRoomId, lastMessage?.id);
      }

      // Join room and update user state
      await socket.join(roomId);
      socket.user.currentRoomId = roomId;
      socket.user.lastActivity = new Date();
      await roomService.updateOnlineMembersCount(roomId, 1);

      // Notify room members
      const userJoinedData = {
        userId: id,
        username: socket.user.username,
        roomId,
        memberCount: roomData.data,
        timestamp: new Date()
      };
      this.emitToRoom(io, roomId, SocketEvent.USER_JOINED, userJoinedData, context);

      // Confirm to user
      this.emitToSocket(socket, SocketEvent.ROOM_JOINED, { room: roomData.data }, context);
      
      console.log(`User ${id} (${socket.user.type}) successfully joined room ${roomId}`);
      return roomData;
    } catch (error: any) {
      console.log(`Error in ${context} for user ${id} joining room ${roomId}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Join room", "Failed to join room");
    }
  }

  /**
   * Removes user from a room
   */
  public static async leaveRoom(
    userId: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<number>> {
    const context = "leaveRoom";
    
    try {
      console.log(`User ${userId} attempting to leave room ${roomId}`);
      
      const { roomService } = await getServices();
      
      if (!checkRateLimit(socket, 'rooms')) {
        console.log(`Rate limit exceeded for user ${userId} leaving room ${roomId}`);
        return ErrorHandler.handle(socket, new Error("Rate limit exceeded"), "Leave room", "Rate limit exceeded");
      }

      let leaveRoomResponse: IResponse<number>;
      
      if (socket.user.type === 'guest') {
        leaveRoomResponse = await roomService.removeGuestFromRoom(userId, roomId);
      } else {
        leaveRoomResponse = { 
          success: false, 
          message: "User type not supported", 
          statusCode: 400, 
          data: null 
        };
      }

      if (!leaveRoomResponse.success) {
        this.emitToSocket(socket, SocketEvent.ERROR, leaveRoomResponse, context);
        console.log(`Failed to remove user ${userId} from room ${roomId}: ${leaveRoomResponse.message}`);
        return leaveRoomResponse;
      }

      // Leave room and update state
      await socket.leave(roomId);
      await roomService.updateOnlineMembersCount(roomId, -1);

      if (socket.user.currentRoomId === roomId) {
        socket.user.currentRoomId = null;
      }

      socket.user.lastActivity = new Date();

      // Notify room members
      const userLeftData = {
        userId,
        userType: socket.user.type,
        username: socket.user.username,
        roomId,
        memberCount: leaveRoomResponse.data,
        timestamp: new Date()
      };
      this.emitToRoom(io, roomId, SocketEvent.USER_LEFT, userLeftData, context);

      // Confirm to user
      this.emitToSocket(socket, SocketEvent.ROOM_LEFT, { roomId }, context);
      
      console.log(`User ${userId} (${socket.user.type}) successfully left room ${roomId}`);
      return leaveRoomResponse;
    } catch (error: any) {
      console.log(`Error in ${context} for user ${userId} leaving room ${roomId}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Leave room", "Failed to leave room");
    }
  }

  /**
   * Creates a new room
   */
  public static async createRoom(
    socket: IGlobalMiddlewareSocket,
    io: Server,
    data: ICreateRoom,
  ): Promise<IResponse<IRoom | null>> {
    const context = "createRoom";
    
    try {
      console.log(`User ${socket.user.id} attempting to create room`, { 
        creatorId: socket.user.id, 
        roomType: data.type,
        guestCount: data.guestIds?.length || 0 
      });
      
      if (!checkRateLimit(socket, 'rooms')) {
        console.log(`Rate limit exceeded for user ${socket.user.id} creating room`);
        return ErrorHandler.handle(socket, new Error("Rate limit exceeded"), "Create room", "Rate limit exceeded");
      }

      const { roomService } = await getServices();
      
      // Validate DM room requirements
      if (data.type === RoomType.dm && data.guestIds.length !== 1) {
        console.log(`Invalid DM room creation: ${data.guestIds.length} recipients specified`);
        return ErrorHandler.handle(
          socket, 
          new Error("DM chats require exactly one recipient"), 
          "Create room", 
          "DM chats require exactly one recipient"
        );
      }

      // Set defaults
      data.creatorId = socket.user.id;
      if (!data.type) {
        data.type = RoomType.group;
      }

      const newRoom = await roomService.createRoom(data);

      if (!newRoom.success) {
        this.emitToSocket(socket, SocketEvent.ERROR, newRoom, context);
        console.log(`Room creation failed for user ${socket.user.id}: ${newRoom.message}`);
        return newRoom;
      }

      // Auto-join creator to the room
      await this.joinRoom(socket.user.id, newRoom.data!.id, socket, io);
      socket.user.lastActivity = new Date();

      // Notify creator of successful creation
      this.emitToSocket(socket, SocketEvent.ROOM_CREATED, newRoom.data, context);

      console.log(`Room ${newRoom.data!.id} successfully created by user ${socket.user.id} (${socket.user.type})`);
      return newRoom;
    } catch (error: any) {
      console.log(`Error in ${context} for user ${socket.user.id}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Create room", "Failed to create room");
    }
  }

  /**
   * Gets media files from a room
   */
  public static async getRoomMedia(
    roomId: string, 
    socket: IGlobalMiddlewareSocket
  ): Promise<IResponse<IRoomMedia[] | null>> {
    const context = "getRoomMedia";
    
    try {
      console.log(`Getting room media for room ${roomId} by user ${socket.user.id}`);
      
      const { roomService } = await getServices();
      const isMember = await roomService.isGuestMemberOfRoom(socket.user.id, roomId);
      
      const errorData: IResponse<null> = {
        success: false,
        message: "User is not a member of this room",
        statusCode: 403,
        data: null
      };

      if (!isMember.success) {
        this.emitToSocket(socket, SocketEvent.ERROR, errorData, context);
        console.log(`Media access denied: User ${socket.user.id} is not a member of room ${roomId}`);
        return errorData;
      }

      const roomMedia = await roomService.getRoomMedia(roomId);
      
      if (!roomMedia.success) {
        this.emitToSocket(socket, SocketEvent.ERROR, roomMedia, context);
        console.log(`Failed to get room media for room ${roomId}: ${roomMedia.message}`);
        return roomMedia;
      }

      this.emitToSocket(socket, SocketEvent.ROOM_MEDIA, roomMedia.data, context);
      console.log(`Retrieved ${roomMedia.data?.length || 0} media items for room ${roomId}`);
      
      return roomMedia;
    } catch (error: any) {
      console.log(`Error in ${context} for room ${roomId} by user ${socket.user.id}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Get room data by id", "Failed to get room data");
    }
  }

  /**
   * Gets messages from a room with pagination
   */
  public static async getRoomMessages(
    roomId: string, 
    socket: IGlobalMiddlewareSocket, 
    limit?: number, 
    cursor?: string
  ) {
    const context = "getRoomMessages";
    
    try {
      console.log(`Getting room messages for room ${roomId}`, { 
        userId: socket.user.id, 
        limit, 
        cursor: cursor ? 'provided' : 'none' 
      });
      
      const { roomService } = await getServices();
      const messages = await roomService.getRoomMessages(roomId, limit, cursor);
      
      if (!messages.success) {
        this.emitToSocket(socket, SocketEvent.ERROR, messages, context);
        console.log(`Failed to get room messages for room ${roomId}: ${messages.message}`);
        return messages;
      }

      this.emitToSocket(socket, SocketEvent.ROOM_MESSAGES, messages.data, context);
      console.log(`Retrieved messages for room ${roomId}`, { 
        messageCount: messages.data?.length || 0,
        // hasMore: messages.data?.hasMore 
      });
      
      return messages;
    } catch (error: any) {
      console.log(`Error in ${context} for room ${roomId} by user ${socket.user.id}: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Get room messages", "Failed to get room messages");
    }
  }
}