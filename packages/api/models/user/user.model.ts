import { z } from "zod";
import { Database, IDatabaseMethodParams, DBLogger, AuthService } from "../../utils";
import { Prisma } from "@prisma/client";
import { ICreateUser, IUserGeneralData, IDelete, IUserDataWithPassword, IUserLoginData } from "./user.types";


/**
 * Represents the User service, extending the generic Database class for the "User" model.
 * Handles user-related operations such as creation, login, updates, and deletions.
 */
export class User extends Database<"User"> {
  private model = "user" as const;

  /**
   * Schema for validating user avatars.
   */
  avatarSchema = z
    .object({
      filename: z.string().optional(),
      fileUrl: z.string().optional(),
    })
    .optional();

  /**
   * Schema for validating user locations.
   */
  locationSchema = z
    .object({
      country: z.string().optional(),
      region: z.string().optional(),
      city: z.string().optional(),
      longitude: z.string().optional(),
      latitude: z.string().optional(),
    })
    .optional();

  /**
   * Enum for user roles.
   */
  roleEnum = z.enum(["Owner", "User"]);

  /**
   * Schema for validating user creation data.
   */
  createSchema = z.object({
    displayName: z.string().min(1, { message: "Name is required" }),
    name: z.string().min(1, { message: "Name is required" }),
    username: z.string().min(1, { message: "Username is required" }),
    email: z.string().email({ message: "Invalid email address" }),
    password: z.string().min(6, { message: "Password must be at least 6 characters" }),
    awayModeEnabled: z.boolean().default(false).optional(),
    avatar: this.avatarSchema,
    location: this.locationSchema,
    role: this.roleEnum.default("User"),
    accountId: this.idSchema.optional(),
    companyIds: z.array(this.idSchema).optional(),
    roomIds: z.array(this.idSchema).optional(),
    analyticId: this.idSchema.optional(),
  });

  /**
   * Schema for validating user update data.
   */
  updateSchema = z.object({
    id: this.idSchema.optional(),
    displayName: z.string().min(1, { message: "Name is required" }).optional(),
    name: z.string().min(1, { message: "Name is required" }).optional(),
    username: z.string().min(1, { message: "Username is required" }).optional(),
    email: z.string().email({ message: "Invalid email address" }).optional(),
    awayModeEnabled: z.boolean().optional(),
    avatar: this.avatarSchema,
    location: this.locationSchema,
    role: this.roleEnum.optional(),
    accountId: this.idSchema.optional(),
    companyIds: z.array(this.idSchema).optional(),
    roomIds: z.array(this.idSchema).optional(),
    analyticId: this.idSchema.optional(),
  });

  constructor() {
    super();
  }

  /**
   * Removes the password field from the provided data object.
   *
   * @template T - The type of the data object.
   * @param {T} data - The data object from which to remove the password.
   * @returns {Omit<T, 'password'>} - The data object without the password field.
   */
  removePassword<T extends Record<string, any>>(data: T): Omit<T, "password"> {
    if (!data || typeof data !== "object") return data;
    const { password, ...rest } = data;
    return rest;
  }

  /**
   * Creates a new user with the provided details and analytic data.
   *
   * @param {Object} data - The data object containing user details and analytic data.
   * @param {ICreateUser} data.details - The user details.
   * @param {IAnalytic} data.analytic - The analytic data.
   * @returns {Promise<IUserGeneralData>} - A promise that resolves to the created user data without the password.
   * @throws {Error} - Throws an error if a user with the provided email already exists or if there is an issue during creation.
   */
  async createUser(data: { details: ICreateUser; analytic: any }): Promise<IUserGeneralData> {
    try {
      const { details, analytic } = data;

      const validatedData = this.validateCreate(details);

      const user: IUserDataWithPassword | null = await this.findUnique({
        where: { email: validatedData.email },
        model: this.model,
      });

      if (user) throw new Error("User with this email already exists");

      const hashedPassword = await AuthService.hashPassword(details.password);

      const dataWithAnalyticCreation = {
        displayName: details.displayName,
        name: details.name,
        username: details.username,
        email: details.email,
        password: hashedPassword,
        awayModeEnabled: details.awayModeEnabled,
        avatar: details.avatar as any,
        location: details.location as any,
        companies: details.companyIds ? { connect: details.companyIds.map((id) => ({ id })) } : undefined,
        rooms: details.roomIds ? { connect: details.roomIds.map((id) => ({ id })) } : undefined,
        analytic: { create: { ...analytic } },
      };

      const params: IDatabaseMethodParams<"user"> = {
        model: this.model,
        data: dataWithAnalyticCreation as Prisma.UserCreateInput,
        include: { analytic: true },
      };

      const result = await this.create(params);
      const resultWithoutPassword: IUserGeneralData = this.removePassword(result);
      return resultWithoutPassword;
    } catch (error) {
      DBLogger.error(`User not created, operationContext: User.createUser, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Logs in a user with the provided credentials.
   *
   * @param {IUserLoginData} data - The login credentials.
   * @returns {Promise<{ token: string; data: IUserGeneralData }>} - A promise that resolves to a token and user data without the password.
   * @throws {Error} - Throws an error if the email or password is invalid.
   */
  async loginUser(data: IUserLoginData): Promise<{ token: string; data: IUserGeneralData }> {
    try {
      const { success } = z.string().email().safeParse(data?.email);
      const { success: successPassword } = z.string().min(6).safeParse(data?.password);
      if (!success || !successPassword) throw new Error("Invalid email or password");

      const user: IUserDataWithPassword | null = await this.findUnique({
        where: { email: data.email },
        model: this.model,
      });

      if(!user) throw new Error("Invalid email or password, signup?");

      const isMatch = data.password ? await AuthService.comparePassword(data.password, user.password) : false;
      if (!isMatch) throw new Error("Invalid email or password, signup?");

      const userDataWithoutPassword = this.removePassword(user) as IUserGeneralData;

      const token = AuthService.generateToken({ id: user.id, email: user.email, isAuthenticated: true, type: "user" });
      return { token, data: userDataWithoutPassword };
    } catch (error) {
      DBLogger.error(`User not logged in, operationContext: User.loginUser, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Retrieves a user by their ID.
   *
   * @param {string} id - The ID of the user to retrieve.
   * @returns {Promise<IUserDataWithPassword>} - A promise that resolves to the user data including the password.
   * @throws {Error} - Throws an error if the user is not found.
   */
  private async getUserById(id: string): Promise<IUserDataWithPassword | null> {
    try {
      const validatedId = this.validateId(id);
      return await this.findUnique({ where: { id: validatedId }, model: this.model });
    } catch (error) {
      DBLogger.error(`User not found, operationContext: User.getUserById, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Retrieves a user by their email.
   *
   * @param {string} email - The email of the user to retrieve.
   * @returns {Promise<IUserDataWithPassword>} - A promise that resolves to the user data including the password.
   * @throws {Error} - Throws an error if the email is invalid or the user is not found.
   */
  private async getUserByEmail(email: string): Promise<IUserDataWithPassword | null> {
    try {
      const validatedEmail = z.string().email().safeParse(email);
      if (!validatedEmail.success) throw new Error("Invalid email address");
      return await this.findUnique({ where: { email: email }, model: this.model });
    } catch (error) {
      DBLogger.error(`User not found, operationContext: User.getUserByEmail, message: ${error.message || error}`);
      throw error;
    }
  }


  /**
   * Updates a user by their ID or email.
   *
   * @param {IUpdateUser} data - The data to update.
   * @param {boolean} confirmExistence - Whether to confirm the existence of the user.
   * @returns {Promise<IUserGeneralData>} - A promise that resolves to the updated user data without the password.
   * @throws {Error} - Throws an error if the user ID or email is invalid or if there is an issue during the update.
   */
  async updateUser(data: Partial<IUserGeneralData>, confirmExistence: boolean = true): Promise<IUserGeneralData> {
    try {
      if(confirmExistence)
      {
        const user = await this.findFirst({ where: {OR: [{id: data.id}, {email: data.email}]}, model: this.model });
        if (!user) {
          throw new Error("The Provided user ID or email does not exist");
        }
      }
      data = this.validateUpdate(data); 
     
      if (data?.id) {
        const { id, ...rest } = data;
        const updateData: Prisma.UserUpdateInput = {
          ...rest,
          location: data.location as any,
          companies: data.companyIds ? { connect: data.companyIds.map((id) => ({ id })) } : undefined,
          rooms: data.roomIds ? { connect: data.roomIds.map((id) => ({ id })) } : undefined,
        };
        const result = await this.update({ model: this.model, where: { id }, data: updateData });
        return this.removePassword(result) as IUserGeneralData;
      }
      if (data?.email) {
        const { email, ...rest } = data;
        const updateData: Prisma.UserUpdateInput = {
          ...rest,
          location: data.location as any,
          companies: data.companyIds ? { connect: data.companyIds.map((id) => ({ id })) } : undefined,
          rooms: data.roomIds ? { connect: data.roomIds.map((id) => ({ id })) } : undefined,
        };
        const result = await this.update({ model: this.model, where: { email: email }, data: updateData });
        return this.removePassword(result) as IUserGeneralData;
      }
      throw new Error("Unable to update user data, please provide a valid user ID or email");
    } catch (error) {
      DBLogger.error(`User not updated, operationContext: User.updateUser, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   *  Deletes a user by their ID or email.
   * @param {Object} deleteOptions - The options for deletion.
   * @param {string} [deleteOptions.id] - The ID of the user to delete.
   * @param {string} [deleteOptions.email] - The email of the user to delete.
   * @param {boolean} confirmExistence - Whether to confirm the existence of the user before deletion.
   * @returns {Promise<IDelete>} - A promise that resolves to a deletion confirmation object.
   * @throws {Error} - Throws an error if the user ID or email is invalid, or if the user does not exist.
   */
  async deleteUser(deleteOptions: { id: string } | { email: string }, confirmExistence: boolean): Promise<IDelete> {
    try {
      let whereCondition: { id: string } | { email: string };
      let errorMessage: string;

      if ("id" in deleteOptions) {
        const validatedId = this.validateId(deleteOptions.id);
        whereCondition = { id: validatedId };
        errorMessage = "The provided user ID does not exist";
      } else {
        const validatedEmail = z.string().email().safeParse(deleteOptions.email);
        if (!validatedEmail.success) throw new Error("Validation failed: Invalid email address");
        whereCondition = { email: deleteOptions.email };
        errorMessage = "The provided user email does not exist";
      }

      if (confirmExistence) {
        const user = await this.findUnique({
          model: this.model,
          where: whereCondition,
        });
        if (!user) throw new Error(errorMessage);
      }

      await this.delete({ model: this.model, where: whereCondition });

      return { data: null } as IDelete;
    } catch (error) {
      DBLogger.error(
        `User not deleted, operationContext: User.deleteUser, message: ${error.message || error}`
      );
      throw error;
    }
  }

  /**
   * Resets a user's password.
   *
   * @param {Object} data - The data for resetting the password.
   * @param {string} data.email - The email of the user.
   * @param {string} data.oldPassword - The old password.
   * @param {string} data.newPassword - The new password.
   * @returns {Promise<boolean>} - A promise that resolves to `true` if the password is reset successfully.
   * @throws {Error} - Throws an error if the email or password is invalid, or if the old and new passwords are the same.
   */
  async changePassword(data: { email: string; oldPassword: string; newPassword: string }): Promise<boolean> {
    try {
      const validatedEmail = z.string().email().safeParse(data.email);
      const validatedPassword = z.string().min(6).safeParse(data.oldPassword);
      const validatedNewPassword = z.string().min(6).safeParse(data.newPassword);
      if (!validatedEmail.success || !validatedPassword.success || !validatedNewPassword.success)
        throw new Error("Invalid email or password");

      //  Intentional set the confusion here on the returning message here. 
      const user = await this.getUserByEmail(data.email);
      if (!user) throw new Error("Invalid email or password");

      if (data.oldPassword === data.newPassword)
        throw new Error("Invalid email or password");

      const isMatch = await AuthService.comparePassword(data.oldPassword, user.password);
      if (!isMatch) throw new Error("Invalid old password provided");

      const hashedPassword = await AuthService.hashPassword(data.newPassword);
      await this.update({ model: this.model, where: { email: data.email }, data: { password: hashedPassword } });
      return true;
    } catch (error) {
      DBLogger.error(`User password not reset, operationContext: User.resetPassword, message: ${error.message || error}`);
      throw error;
    }
  }
}
