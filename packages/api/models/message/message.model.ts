import { z } from "zod";
import { Database, DBLogger } from "../../utils/database";
import { IMessage, ICreateMessage, IUpdateMessage, ISearchFilters, ISearchOptions, ISearchResult, IMessageStats, MessageWithRelations } from "./message.types"
import { get } from "http";



export class Message extends Database<"Message"> {
    private model = 'message' as const;

    createSchema = z.object({
        text: z.string().min(1, { message: 'Text is required' }).optional(),
        isPinned: z.boolean().optional(),
        isAnswered: z.boolean().optional(),
        isEncrypted: z.boolean().optional(),
        status: z.enum(["Sent", "Delivered", "Read"]).optional(),
        parentId: z.string().min(1, { message: 'Parent ID is required' }).optional(),
        senderId: z.string().min(1, { message: 'Sender ID is required' }),
        to: z.string().min(1, { message: 'To is required' }),
        files: z.array(z.string()).optional(),
    });

    updateSchema = z.object({
        text: z.string().min(1, { message: 'Text is required' }).optional(),
        isPinned: z.boolean().optional(),
        isAnswered: z.boolean().optional(),
        isEncrypted: z.boolean().optional(),
        status: z.enum(["Sent", "Delivered", "Read"]).optional(),
        updatedAt: z.date().optional(),
        read: z.boolean().optional(),
        edited: z.boolean().optional(),
    });

    // Search schema validation
    searchSchema = z.object({
        words: z.array(z.string().min(1)).min(1, { message: 'At least one search word is required' }),
        roomId: z.string().optional(),
        roomIds: z.array(z.string()).optional(),
        filters: z.object({
            senderId: z.string().optional(),
            senderType: z.enum(['admin', 'chatAgent', 'aiAgent', 'guest']).optional(),
            dateFrom: z.date().optional(),
            dateTo: z.date().optional(),
            hasFiles: z.boolean().optional(),
            fileTypes: z.array(z.string()).optional(),
            isPinned: z.boolean().optional(),
            isAnswered: z.boolean().optional(),
            status: z.enum(["Sent", "Delivered", "Read"]).optional(),
            parentId: z.string().optional(),
        }).optional(),
        options: z.object({
            limit: z.number().min(1).max(100).optional(),
            offset: z.number().min(0).optional(),
            sortBy: z.enum(['createdAt', 'updatedAt', 'relevance']).optional(),
            sortOrder: z.enum(['asc', 'desc']).optional(),
        }).optional(),
    });

    async createMessage(data: ICreateMessage): Promise<IMessage> {
        try {
            const validatedData = this.validateCreate(data);
            const { files: fileIds } = data;

            const result = await this.prisma.message.create({
                data: {
                    ...validatedData,
                    files: fileIds?.length ? {
                        connect: fileIds.map(fileId => ({ id: fileId }))
                    } : undefined
                },
                include: { 
                    files: { 
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                     },
                     sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    } 
                }
            });
            return Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Message not created, operationContext: Message.createMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    async updateMessage(id: string, data: IUpdateMessage): Promise<IUpdateMessage> {
        try {
            this.validateId(id);
            const validatedData = this.validateUpdate(data);
            const result = await this.prisma.message.update({
                where: { id },
                data: validatedData,
                select: {
                    text: true,
                    isPinned: true,
                    isAnswered: true,
                    isEncrypted: true,
                    status: true,
                    updatedAt: true,
                    read: true,
                }
            });
            return result;
        } catch (error) {
            DBLogger.error(`Message not updated, operationContext: Message.updateMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    async deleteMessage(id: string): Promise<boolean> {
        try {
            this.validateId(id);
            await this.prisma.message.delete({
                where: { id }
            });
            return true;
        } catch (error) {
            DBLogger.error(`Message not deleted, operationContext: Message.deleteMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    async editMessage(id: string, text: string): Promise<IMessage> {
        try {
            this.validateId(id);
            const validatedData = this.validateUpdate({ text });
            const result = await this.prisma.message.update({
                where: { id },
                data: {
                    ...validatedData,
                    edited: true
                },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });
            return Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Message not edited, operationContext: Message.editMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Mark multiple messages as read
     */
    async markMessageRead(ids: string[]): Promise<boolean> {
        try {
            ids.map(id => this.validateId(id));
            const result = await this.prisma.message.updateMany({
                where: { id: { in: ids } },
                data: {
                    read: true
                }
            });
            return result.count === ids.length;
        } catch (error) {
            DBLogger.error(`Message not marked as read, operationContext: Message.markMessageRead, message: ${error.message || error}`);
            throw error;
        }
    }

    async createThread(parentId: string, data: ICreateMessage): Promise<IMessage> {
        try {
            this.validateId(parentId);
            const validatedData = this.validateCreate(data);
            const result = await this.prisma.message.create({
                data: {
                    ...validatedData,
                    parentId
                },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });

            return Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Thread not created, operationContext: Message.createThread, message: ${error.message || error}`);
            throw error;
        }
    }

    public static TransformMessageToIMessage(message: any): IMessage {
        return {
            ...message,
            files: message.files.map((file: any) => {
                const { id, filename, fileUrl, fileType, size, createdAt, uploadedBy } = file;
                return { id, filename, fileUrl, fileType, size, createdAt, uploader: uploadedBy };
            }),
            sender: {
                id: message.sender.id,
                name: message.sender.name,
                username: message.sender.username,
                avatar: message.sender.avatar
            }
        }
    }

    async getThread(id: string): Promise<IMessage[]> {
        try {
            this.validateId(id);
            const result = await this.prisma.message.findMany({
                where: {
                    parentId: id
                },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                },
                orderBy: {
                    createdAt: 'asc'
                }
            });
            return result.map(Message.TransformMessageToIMessage);
        } catch (error) {
            DBLogger.error(`Thread not found, operationContext: Message.getThread, message: ${error.message || error}`);
            throw error;
        }
    }

    async getMessageById(id: string): Promise<IMessage | null> {
        try {
            this.validateId(id);
            const result = await this.prisma.message.findUnique({
                where: { id },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });
            if (!result) return null;
            return Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Message not found, operationContext: Message.getMessageById, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Search for words in a specific room
     */
    async searchMessagesInRoom(
        roomId: string, 
        words: string[], 
        filters?: ISearchFilters, 
        options?: ISearchOptions
    ): Promise<ISearchResult> {
        try {
            this.validateId(roomId);
            
            const searchData = this.searchSchema.parse({ 
                words, 
                roomId, 
                filters, 
                options 
            });

            const whereClause = this.buildSearchWhereClause(
                searchData.words, 
                [roomId], 
                searchData.filters
            );

            const { limit = 20, offset = 0, sortBy = 'createdAt', sortOrder = 'desc' } = options || {};

            // Get total count for pagination
            const totalCount = await this.prisma.message.count({
                where: whereClause
            });

            // Get messages
            const messages = await this.prisma.message.findMany({
                where: whereClause,
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    },
                    room: {
                        select: { id: true, name: true, type: true }
                    }
                },
                orderBy: {
                    [sortBy]: sortOrder
                },
                take: limit,
                skip: offset
            });

            return {
                messages: messages.map(Message.TransformMessageToIMessage),
                totalCount,
                hasMore: offset + messages.length < totalCount
            };

        } catch (error) {
            DBLogger.error(`Message search failed in room, operationContext: Message.searchMessagesInRoom, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Search for words across multiple rooms
     */
    async searchMessagesInRooms(
        words: string[], 
        roomIds: string[], 
        filters?: ISearchFilters, 
        options?: ISearchOptions
    ): Promise<ISearchResult> {
        try {
            roomIds.forEach(id => this.validateId(id));
            
            const searchData = this.searchSchema.parse({ 
                words, 
                roomIds, 
                filters, 
                options 
            });

            const whereClause = this.buildSearchWhereClause(
                searchData.words, 
                roomIds, 
                searchData.filters
            );

            const { limit = 20, offset = 0, sortBy = 'createdAt', sortOrder = 'desc' } = options || {};

            // Get total count for pagination
            const totalCount = await this.prisma.message.count({
                where: whereClause
            });

            // Get messages
            const messages = await this.prisma.message.findMany({
                where: whereClause,
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    },
                    room: {
                        select: { id: true, name: true, type: true }
                    }
                },
                orderBy: {
                    [sortBy]: sortOrder
                },
                take: limit,
                skip: offset
            });

            return {
                messages: messages.map(Message.TransformMessageToIMessage),
                totalCount,
                hasMore: offset + messages.length < totalCount
            };

        } catch (error) {
            DBLogger.error(`Message search failed across rooms, operationContext: Message.searchMessagesInRooms, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Global search across all rooms a user has access to
     */
    async globalMessageSearch(
        userId: string,
        words: string[],
        roomIds: string[] = [],
        filters?: ISearchFilters,
        options?: ISearchOptions
    ): Promise<ISearchResult> {
        try {
            this.validateId(userId);
            if (roomIds && roomIds.length > 0) {
                roomIds.forEach(id => this.validateId(id));
                // check if user is member
                const isMember = await this.prisma.room.findMany({
                    where: {
                        id: { in: roomIds },
                        OR: [
                            { userIds: { has: userId } },
                            { guestIds: { has: userId } }
                        ]
                    }
                });
                // eliminate room the user is not a member of
                roomIds = roomIds.filter(id => isMember.some((room: any) => room.id === id));
            } else { 

                // First get all rooms the user has access to
                const userRooms = await this.prisma.room.findMany({
                    where: {
                        OR: [
                            { userIds: { has: userId } },
                            { guestIds: { has: userId } }
                        ]
                    },
                    select: { id: true }
                });

                roomIds = userRooms.map(room => room.id);

                if (roomIds.length === 0) {
                    return {
                        messages: [],
                        totalCount: 0,
                        hasMore: false
                    };
                }
            }

            return await this.searchMessagesInRooms(words, roomIds, filters, options);

        } catch (error) {
            DBLogger.error(`Global message search failed, operationContext: Message.globalMessageSearch, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Search messages by sender
     */
    async searchMessagesBySender(
        senderId: string,
        roomIds?: string[],
        filters?: Omit<ISearchFilters, 'senderId'>,
        options?: ISearchOptions
    ): Promise<ISearchResult> {
        try {
            this.validateId(senderId);

            const enhancedFilters = { ...filters, senderId };
            
            if (roomIds && roomIds.length > 0) {
                return await this.searchMessagesInRooms([], roomIds, enhancedFilters, options);
            }

            // Search across all messages by this sender
            const whereClause = this.buildSearchWhereClause([], [], enhancedFilters);
            const { limit = 20, offset = 0, sortBy = 'createdAt', sortOrder = 'desc' } = options || {};

            const totalCount = await this.prisma.message.count({
                where: whereClause
            });

            const messages = await this.prisma.message.findMany({
                where: whereClause,
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    },
                    room: {
                        select: { id: true, name: true, type: true }
                    }
                },
                orderBy: {
                    [sortBy]: sortOrder
                },
                take: limit,
                skip: offset
            });

            return {
                messages: messages.map(Message.TransformMessageToIMessage),
                totalCount,
                hasMore: offset + messages.length < totalCount
            };

        } catch (error: any) {
            DBLogger.error(`Message search by sender failed, operationContext: Message.searchMessagesBySender, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Search for messages with files
     */
    async searchMessagesWithFiles(
        roomIds: string[],
        fileTypes?: string[],
        filters?: ISearchFilters,
        options?: ISearchOptions
    ): Promise<ISearchResult> {
        try {
            const enhancedFilters = { 
                ...filters, 
                hasFiles: true, 
                fileTypes: fileTypes || filters?.fileTypes 
            };
            
            return await this.searchMessagesInRooms([], roomIds, enhancedFilters, options);

        } catch (error: any) {
            DBLogger.error(`Message search with files failed, operationContext: Message.searchMessagesWithFiles, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Get last message in a room
     */
    async getLastMessageInRoom(roomId: string): Promise<IMessage | null> {
        try {
            this.validateId(roomId);
            const message = await this.prisma.message.findFirst({
                where: {
                    to: roomId
                },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
            if (!message) return null;
            return Message.TransformMessageToIMessage(message);
        } catch (error: any) {
            DBLogger.error(`Last message fetch failed, operationContext: Message.getLastMessageInRoom, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Get all the messages that the guest has missed in the room
     * Return messages from oldest to newest
     */
    async getGuestMissedMessages(guestId: string, roomId: string): Promise<IMessage[]> {
        try {
            this.validateId(guestId);
            this.validateId(roomId);
            const guestRoomActivity = await this.prisma.guestRoomActivity.findUnique({
                where: { guestId_roomId: { guestId, roomId } }
            });
            if(!guestRoomActivity) {
                return [];
            }
            const { lastReadMessageId, lastReadAt }= guestRoomActivity;
            let result: MessageWithRelations[] = [];
            let whereClause: any = {
                to: roomId,
                senderId: {
                    not: guestId
                }
            };
            if(lastReadMessageId) {
                whereClause.id = { gt: lastReadMessageId };
            } else {
                whereClause.createdAt = { gt: lastReadAt };
            }
            result = await this.prisma.message.findMany({
                where: whereClause,
                include: {
                    files: {
                        include: {
                            guest: true,
                        },
                    },
                    sender: true,
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
            return result.map(Message.TransformMessageToIMessage).reverse();
        } catch (error) {
            DBLogger.error('Error getting guest missed messages, operationContext: Message.getGuestMissedMessages:', error.message);
            throw error;
        }
    }

    /**
     * Get a Guest last Read Message Id and the read timestamp
     */
    async getGuestLastReadMessage(guestId: string, roomId: string): Promise<{ lastReadMessageId?: string, lastReadAt?: Date }> {
        try {
            this.validateId(guestId);
            this.validateId(roomId);
            const guestRoomActivity = await this.prisma.guestRoomActivity.findUnique({
                where: { guestId_roomId: { guestId, roomId } }
            });
            if(!guestRoomActivity) {
                return { lastReadMessageId: null, lastReadAt: null };
            }
            return { lastReadMessageId: guestRoomActivity.lastReadMessageId, lastReadAt: guestRoomActivity.lastReadAt };
        } catch (error) {
            DBLogger.error('Error getting guest last read message, operationContext: Message.getGuestLastReadMessage:', error.message);
            throw error;
        }
    }

    /**
     * Get recent messages from a room
     */
    async getRecentMessages(
        roomId: string,
        limit: number = 50,
        before?: Date
    ): Promise<IMessage[]> {
        try {
            this.validateId(roomId);

            const whereClause: any = {
                to: roomId,
                parentId: null // Only get root messages, not thread replies
            };

            if (before) {
                whereClause.createdAt = {
                    lt: before
                };
            }

            const messages = await this.prisma.message.findMany({
                where: whereClause,
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                },
                take: limit
            });

            return messages.map(Message.TransformMessageToIMessage).reverse(); // Reverse to get chronological order

        } catch (error: any) {
            DBLogger.error(`Recent messages fetch failed, operationContext: Message.getRecentMessages, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Get unread messages count for a user in rooms
     */
    async getUnreadMessagesCount(userId: string, roomIds?: string[]): Promise<number> {
        try {
            this.validateId(userId);

            const whereClause: any = {
                read: false,
                senderId: {
                    not: userId // Don't count user's own messages
                }
            };

            if (roomIds && roomIds.length > 0) {
                whereClause.to = {
                    in: roomIds
                };
            } else {
                // Get all rooms the user has access to
                const userRooms = await this.prisma.room.findMany({
                    where: {
                        OR: [
                            { userIds: { has: userId } },
                            { guestIds: { has: userId } }
                        ]
                    },
                    select: { id: true }
                });

                whereClause.to = {
                    in: userRooms.map(room => room.id)
                };
            }

            return await this.prisma.message.count({
                where: whereClause
            });

        } catch (error: any) {
            DBLogger.error(`Unread messages count failed, operationContext: Message.getUnreadMessagesCount, message: ${error.message || error}`);
            throw error;
        }
    }

    async getUnreadMessages(userId: string, roomIds?: string[]): Promise<IMessage[]> {
        try {
            this.validateId(userId);

            const whereClause: any = {
                read: false,
                senderId: {
                    not: userId // Don't count user's own messages
                }
            };

            if (roomIds && roomIds.length > 0) {
                whereClause.to = {
                    in: roomIds
                };
            } else {
                // Get all rooms the user has access to
                const userRooms = await this.prisma.room.findMany({
                    where: {
                        OR: [
                            { userIds: { has: userId } },
                            { guestIds: { has: userId } }
                        ]
                    },
                    select: { id: true }
                });

                whereClause.to = {
                    in: userRooms.map(room => room.id)
                };
            }

            const messages = await this.prisma.message.findMany({
                where: whereClause,
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });

            return messages.map(Message.TransformMessageToIMessage);

        } catch (error: any) {
            DBLogger.error(`Unread messages fetch failed, operationContext: Message.getUnreadMessages, message: ${error.message || error}`);
            throw error;
        }
    }

    async getMessageStatistics(roomIds: string[], startDate: Date, endDate: Date): Promise<IMessageStats> {
        try {
            roomIds.forEach(id => this.validateId(id));

            // TODO: Implement this method
            return {
                totalMessages: 0,
                messagesByStatus: { sent: 0, delivered: 0, read: 0 },
                messagesByType: { text: 0, withFiles: 0, threads: 0, pinned: 0 },
                topSenders: [],
                messagesOverTime: []
            };

        } catch (error: any) {
            DBLogger.error(`Message statistics failed, operationContext: Message.getMessageStatistics, message: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Build where clause for search functionality
     */
    private buildSearchWhereClause(
        words: string[], 
        roomIds: string[], 
        filters?: ISearchFilters
    ): any {
        const whereClause: any = {};

        // Room filtering
        if (roomIds.length > 0) {
            whereClause.to = roomIds.length === 1 ? roomIds[0] : { in: roomIds };
        }

        // Text search - MongoDB text search or contains
        if (words.length > 0) {
            // On MongoDB Atlas, will later change to using full-text search when the text field has been set as a text indexes
            // Now, using contains for each word
            whereClause.OR = words.map(word => ({
                text: {
                    contains: word,
                    mode: 'insensitive'
                }
            }));
        }

        // Apply filters
        if (filters) {
            if (filters.senderId) {
                whereClause.senderId = filters.senderId;
            }

            if (filters.dateFrom || filters.dateTo) {
                whereClause.createdAt = {};
                if (filters.dateFrom) {
                    whereClause.createdAt.gte = filters.dateFrom;
                }
                if (filters.dateTo) {
                    whereClause.createdAt.lte = filters.dateTo;
                }
            }

            if (filters.hasFiles !== undefined) {
                if (filters.hasFiles) {
                    whereClause.files = {
                        some: {}
                    };
                } else {
                    whereClause.files = {
                        none: {}
                    };
                }
            }

            if (filters.fileTypes && filters.fileTypes.length > 0) {
                whereClause.files = {
                    some: {
                        fileType: {
                            in: filters.fileTypes
                        }
                    }
                };
            }

            if (filters.isPinned !== undefined) {
                whereClause.isPinned = filters.isPinned;
            }

            if (filters.isAnswered !== undefined) {
                whereClause.isAnswered = filters.isAnswered;
            }

            if (filters.status) {
                whereClause.status = filters.status;
            }

            if (filters.parentId !== undefined) {
                whereClause.parentId = filters.parentId;
            }
        }

        return whereClause;
    }
}