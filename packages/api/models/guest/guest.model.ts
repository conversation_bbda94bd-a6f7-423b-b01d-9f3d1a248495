import { z } from "zod";
import { Database, DBLogger, storage } from "../../utils";
import { IG<PERSON>, IGuestMetaData } from "./guest.types";


export class Guest extends Database<"Guest"> {
  private model = "guest" as const;

  locationSchema = z.object({
    country: z.string().optional(),
    region: z.string().optional(),
    city: z.string().optional(),
    longitude: z.string().optional(),
    latitude: z.string().optional(),
  }).optional();

  avatarSchema = z.object({
    filename: z.string(),
    fileUrl: z.string(),
  }).optional();

  createSchema = z.object({
    displayName: z.string().optional(),
    name: z.string().min(1, "Name is required").optional(),
    username: z.string().optional(),
    externalId: z.string().min(1, "External ID is required"),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    avatar: this.avatarSchema,
    location: this.locationSchema,
    companyId: z.string(),
    applicationIds: z.array(z.string()).optional(),
  });

  updateSchema = z.object({
    name: z.string().optional(),
    username: z.string().optional(),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    lastSeenAt: z.date().optional(),
    displayName: z.string().optional(),
    awayModeEnabled: z.boolean().optional(),
    avatar: this.avatarSchema,
    location: this.locationSchema,
    hasHardBounced: z.boolean().optional(),
    markedEmailAsSpam: z.boolean().optional(),
  });

  constructor() {
    super();
  }

  
  public async upsert(
    externalId: string,
    companyId: string,
    metaData: IGuestMetaData = {}
  ): Promise<IGuest> {
    try {
      if (!externalId) {
        throw new Error('Validation failed: Missing externalId field');
      }
  
      const compositeExternalId = `${externalId}+${companyId}`;
      const existingGuest = await this.prisma.guest.findUnique({
        where: { externalId: compositeExternalId },
      });

      if (metaData.avatar && metaData.avatar.fileUrl) {
        const createAvatar = await storage.downloadAndStoreWithAutoKey(
          metaData.avatar.fileUrl,
          "sparkstrandchat/guests", 
          {
            cacheControl: 'public, max-age=2592000', // 30 days
            metadata: {
              uploadedBy: externalId,
              uploadType: 'avatar',
              timestamp: new Date().toISOString()
            }
          }
        );
        metaData.avatar = {
          filename: createAvatar.key,
          fileUrl: createAvatar.url,
        };
      }
  
      // Update existing guest if metadata provided
      if (existingGuest) {
        if (Object.keys(metaData).length > 0) {
          const updateData = this.buildUpdateData(metaData);
          return await this.prisma.guest.update({
            where: { id: existingGuest.id },
            data: updateData,
          });
        }
        return existingGuest;
      }
  
      // Create new guest
      this.validateCreate({ ...metaData, externalId, companyId });
      
      const guestData = await this.buildGuestData(
        metaData, 
        externalId, 
        companyId, 
        compositeExternalId,
      );
  
      const newGuest = await this.prisma.guest.create({
        data: {
          ...guestData,
          analytic: { create: {} },
        },
      });
  
      // Update relations in a single transaction
      await this.prisma.$transaction([
        this.prisma.application.update({
          where: { id: guestData.application.connect.id },
          data: { guests: { connect: { id: newGuest.id } } },
        }),
        this.prisma.company.update({
          where: { id: companyId },
          data: { guests: { connect: { id: newGuest.id } } },
        }),
      ]);
      
      const { companyIds, ...rest } = newGuest;
      return rest;
  
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      if(error.includes("Failed to fetch external file:")){
       throw new Error("The provided metaData.avatar.fileUrl does not exist"); 
      }
      DBLogger.error(`Guest creation failed, operationContext: Guest.findOrCreateGuest, message: ${errorMessage}`);
      throw error;
    }
  }
  
  private buildUpdateData(metaData: IGuestMetaData) {
    const updateData: any = {};
  
    if (metaData.name) updateData.name = metaData.name;
    if (metaData.email) updateData.email = metaData.email;
    if (metaData.phone) updateData.phone = metaData.phone;
    if (metaData.displayName) updateData.displayName = metaData.displayName;
    if (metaData.lastSeenAt) updateData.lastSeenAt = metaData.lastSeenAt;
    if (metaData.location) updateData.location = metaData.location;
    if (metaData.avatar) {
      updateData.avatar = {
        filename: metaData.avatar.filename || 'avatar.jpg',
        fileUrl: metaData.avatar.fileUrl || '',
      };
    }
  
    return updateData;
  }
  
  private async  buildGuestData(
    metaData: IGuestMetaData,
    externalId: string,
    companyId: string,
    compositeExternalId: string,
  ) {
    const displayName = metaData.displayName || `Guest-${metaData.name || 'unknown'}`;
    const username = metaData.username || `guest-${externalId.slice(0, 8)}`;
  
    const guestData: any = {
      displayName,
      name: metaData.name || displayName,
      username,
      externalId: compositeExternalId,
      email: metaData.email,
      phone: metaData.phone,
      companies: { connect: [{ id: companyId }] },
    };
  
    // Resolve application
    if (metaData.applicationId) {
      guestData.application = { connect: { id: metaData.applicationId } };
    } else {
      const application = await this.prisma.application.findUnique({
        where: { companyId_type: { companyId, type: 'General' } },
      });
      if (!application) {
        throw new Error('No default application found for company');
      }
      guestData.application = { connect: { id: application.id } };
    }
  
    if (metaData.avatar) {
      guestData.avatar = {
        filename: metaData.avatar.filename || 'avatar.jpg',
        fileUrl: metaData.avatar.fileUrl || '',
      };
    }
  
    if (metaData.location) {
      guestData.location = metaData.location;
    }
  
    return guestData;
  }
  

  async getGuestByIdAndCompanyId(guestId: string, companyId: string): Promise<IGuest | null> {
    try {
      this.validateId(companyId);
      this.validateId(guestId);
      const guest = await this.prisma.guest.findFirst({
        where: {
          id: guestId,
          companyIds: {
            has: companyId
          }
        }
      });
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Get guest failed, operationContext: Guest.getGuestByCIdAndCompanyId, message: ${error.message || error}`);
      throw error;
    }
  }

  async getGuestByExternalIdAndCompanyId(externalId: string, companyId: string): Promise<IGuest | null> {
    try {
      this.validateId(companyId);
      const guest = await this.prisma.guest.findUnique({
        where: {
          externalId: `${externalId}+${companyId}`
        }
      });
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Get guest failed, operationContext: Guest.getGuestByExternalIdAndCompanyId, message: ${error.message || error}`);
      throw error;
    }
  }

  async getGuestById(id: string): Promise<IGuest | null> {
    try {
      this.validateId(id);
      const guest = await this.prisma.guest.findUnique({
        where: { id }
      });
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Get guest failed, operationContext: Guest.getGuestById, message: ${error.message || error}`);
      throw error;
    }
  }

  async getGuestByExternalId(externalId: string): Promise<IGuest | null> {
    try {
      const guest = await this.prisma.guest.findUnique({
        where: { externalId }
      });
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Get guest failed, operationContext: Guest.getGuestByExternalId, message: ${error.message || error}`);
      throw error;
    }
  }

  async updateGuest(id: string, data: z.infer<typeof this.updateSchema>): Promise<IGuest> {
    try {
      this.validateId(id);
      const parsedData = this.validateUpdate(data);
      
      const guest = await this.prisma.guest.update({
        where: { id },
        data: parsedData
      });
      
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Guest update failed, operationContext: Guest.updateGuest, message: ${error.message || error}`);
      throw error;
    }
  }

  async deleteGuest(id: string): Promise<void> {
    try {
      this.validateId(id);
      const guest = await this.prisma.guest.delete({
        where: { id }
      });
      
      return;
    } catch (error) {
      DBLogger.error(`Guest deletion failed, operationContext: Guest.deleteGuest, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * GuestRoomActivity
   */
  async guestRoomActivity(guestId: string, roomId: string, messageId?: string): Promise<void> {
    try {
      this.validateId(guestId);
      this.validateId(roomId);
      if(messageId)
        this.validateId(messageId);
      const guest = await this.prisma.guestRoomActivity.upsert({
        where: { guestId_roomId: { guestId, roomId } },
        update: { lastReadMessageId: messageId, lastReadAt: new Date() },
        create: { guestId, roomId, lastReadMessageId: messageId, lastReadAt: new Date() }
      });
      
      return;
    } catch (error) {
      DBLogger.error(`Guest last read message update failed, operationContext: Guest.updateGuestLastReadMessage, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Check if a guest belongs to both an application and a room
   */
  async isGuestInApplicationAndRoom(guestId: string, applicationId: string, roomId: string): Promise<boolean> {
    try {
      this.validateId(guestId);
      this.validateId(applicationId);
      this.validateId(roomId);
      const guest = await this.prisma.guest.findFirst({
        where: {
          id: guestId,
          applicationIds: {
            has: applicationId
          },
          roomIds: {
            has: roomId
          }
        }
      });
      
      return !!guest;
    } catch (error) {
      DBLogger.error(`Check guest in application and room failed, operationContext: Guest.isGuestInApplicationAndRoom, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Check if a guest belongs to an application
   */
  async isGuestInApplication(guestId: string, applicationId: string): Promise<boolean> {
    try {
      this.validateId(guestId);
      this.validateId(applicationId);
      const guest = await this.prisma.guest.findFirst({
        where: {
          id: guestId,
          applicationIds: {
            has: applicationId
          }
        }
      });
      
      return !!guest;
    } catch (error) {
      DBLogger.error(`Check guest in application failed, operationContext: Guest.isGuestInApplication, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Check if a guest belongs to a specific room
   */
  async isGuestInRoom(guestId: string, roomId: string): Promise<boolean> {
    try {
      this.validateId(guestId);
      this.validateId(roomId);
      const guest = await this.prisma.guest.findFirst({
        where: {
          id: guestId,
          roomIds: {
            has: roomId
          }
        }
      });
      
      return !!guest;
    } catch (error) {
      DBLogger.error(`Check guest in room failed, operationContext: Guest.isGuestInRoom, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Check if a guest belongs to a list of rooms
   */
  async isGuestInRooms(guestId: string, roomIds: string[]): Promise<boolean> {
    try {
      this.validateId(guestId);
      roomIds.forEach(this.validateId);
      const guest = await this.prisma.guest.findUnique({
        where: { id: guestId },
        select: { roomIds: true }
      });
      
      if (!guest) return false;
      
      return roomIds.every(roomId => guest.roomIds.includes(roomId));
    } catch (error) {
      DBLogger.error(`Check guest in rooms failed, operationContext: Guest.isGuestInRooms, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Check if a guest belongs to a list of applications
   */
  async isGuestInApplications(guestId: string, applicationIds: string[]): Promise<boolean> {
    try {
      this.validateId(guestId);
      applicationIds.forEach(this.validateId);
      const guest = await this.prisma.guest.findUnique({
        where: { id: guestId },
        select: { applicationIds: true }
      });
      
      if (!guest) return false;
      
      return applicationIds.every(appId => guest.applicationIds.includes(appId));
    } catch (error) {
      DBLogger.error(`Check guest in applications failed, operationContext: Guest.isGuestInApplications, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Add a guest to a room
   */
  async addGuestToRoom(guestId: string, roomId: string): Promise<IGuest> {
    try {
      this.validateId(guestId);
      this.validateId(roomId);
      const guest = await this.prisma.guest.update({
        where: { id: guestId },
        data: {
          rooms: {
            connect: { id: roomId }
          }
        }
      });

      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
      
    } catch (error) {
      DBLogger.error(`Add guest to room failed, operationContext: Guest.addGuestToRoom, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Add a guest to an application
   */
  async addGuestToApplication(guestId: string, applicationId: string): Promise<IGuest> {
    try {
      this.validateId(guestId);
      this.validateId(applicationId);
      const guest = await this.prisma.guest.update({
        where: { id: guestId },
        data: {
          application: {
            connect: { id: applicationId }
          }
        }
      });
      
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Add guest to application failed, operationContext: Guest.addGuestToApplication, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Remove a guest from a room
   */
  async removeGuestFromRoom(guestId: string, roomId: string): Promise<IGuest> {
    try {
      this.validateId(guestId);
      this.validateId(roomId);
      const guest = await this.prisma.guest.update({
        where: { id: guestId },
        data: {
          rooms: {
            disconnect: { id: roomId }
          }
        }
      });
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Remove guest from room failed, operationContext: Guest.removeGuestFromRoom, message: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Remove a guest from an application
   */
  async removeGuestFromApplication(guestId: string, applicationId: string): Promise<IGuest> {
    try {
      this.validateId(guestId);
      this.validateId(applicationId);
      
      const guest = await this.prisma.guest.update({
        where: { id: guestId },
        data: {
          application: {
            disconnect: { id: applicationId }
          }
        }
      });
      
      if(!guest) return null;
      
      const { companyIds, ...rest } = guest;
      return rest;
    } catch (error) {
      DBLogger.error(`Remove guest from application failed, operationContext: Guest.removeGuestFromApplication, message: ${error.message || error}`);
      throw error;
    }
  }
}
