import { z } from "zod";
import { Database, DBLogger, storage } from "../../utils";
import { 
    <PERSON>R<PERSON>, ICreateRoom, 
    IUpdateRoom, RoomType, 
    IRoomGuests, RoomWithRelations, IRoomMedia,
} from "./room.types";

import { IRoomPermissionType, IPermissionEntityType, IRoomPermission } from "../roomPermission/roomPermission.types";

import { 
    IMessage, IMessageFileAttachement,
    IMessageFileUploader, IMessageSender, 
    MessageWithRelations 
} from "../message/message.types";



export class Room extends Database<"Room"> {
    private model = 'room' as const;

    avatarSchema = z.object({
        filename: z.string(),
        fileUrl: z.string().url()
    }).optional();

    createSchema = z.object({
        name: z.string().min(1, { message: 'Room name is required' }),
        description: z.string().optional(),
        avatar: this.avatarSchema,
        applicationId: z.string().min(1, { message: 'Application ID is required' }),
        membersCount: z.number().int().nonnegative().optional().default(0),
        userIds: z.array(z.string()).optional().default([]),
        guestIds: z.array(z.string()).optional().default([]),
        tagIds: z.array(z.string()).optional().default([]),
        anonymousIds: z.array(z.string()).optional().default([]),
        type: z.nativeEnum(RoomType).optional().default(RoomType.dm),
        setting: z.record(z.any()).optional(),
        creatorId: z.string().optional(),
        metaData: z.record(z.any()).optional(),
        expiresAt: z.date().optional(),
    });

    updateSchema = z.object({
        name: z.string().optional(),
        description: z.string().optional(),
        avatar: this.avatarSchema,
        archived: z.boolean().optional(),
        userIds: z.array(z.string()).optional(),
        guestIds: z.array(z.string()).optional(),
        tagIds: z.array(z.string()).optional(),
        anonymousIds: z.array(z.string()).optional(),
        membersCount: z.number().int().nonnegative().optional(),
        onlineMembersCount: z.number().int().nonnegative().optional(),
        type: z.nativeEnum(RoomType).optional(),
        setting: z.record(z.any()).optional(),
        expiresAt: z.date().optional(),
    });

    constructor() {
        super();
    }
     public static transformMessageToIMessage(message: MessageWithRelations): IMessage {
        try {
            return {
                id: message.id,
                text: message?.text || undefined,
                isPinned: message.isPinned,
                isAnswered: message.isAnswered,
                isEncrypted: message.isEncrypted,
                status: message.status as IMessage['status'],
                parentId: message?.parentId || undefined,
                createdAt: message.createdAt,
                updatedAt: message.updatedAt,
                read: message.read,
                edited: message.edited,
                to: message.to,
                sender: {
                    id: message.sender.id,
                    name: message.sender.name,
                    username: message.sender.username,
                    avatar: message.sender?.avatar || { filename: '', fileUrl: '' },
                } as IMessageSender,
                files: message?.files.map((file) => ({
                    id: file.id,
                    filename: file.filename,
                    fileUrl: file.fileUrl,
                    fileType: file.fileType || undefined,
                    size: file.size,
                    createdAt: file.createdAt,
                    uploader: {
                        id: file.uploadedBy,
                        name: file.guest?.name  || 'Unknown',
                        username: file.guest?.username || 'unknown',
                    } as IMessageFileUploader,
                })) as IMessageFileAttachement[],
            };
        } catch (error) {
            DBLogger.error('Error transforming message to IMessage, operationContext: Room.transformMessageToIMessage:', error);
            throw error;
        }
    }

    public static transformRoomToIRoom(room: RoomWithRelations): IRoom {
        try {
            // Transform permissions
            const permissions = room.permissions.map((perm: any) => ({
                id: perm.id,
                entityType: perm.entityType as IPermissionEntityType,
                entityId: perm.entityId,
                permission: perm.permission as IRoomPermissionType,
                isAllowed: perm.isAllowed,
            })) as IRoomPermission[];

            // Categorize guests based on permissions
            const admins: IRoomGuests[] = [];
            const moderators: IRoomGuests[] = [];
            const members: IRoomGuests[] = [];

            // Process users and guests to assign roles
            const allGuests = room.guests;

            allGuests.forEach((entity) => {
                const guest: IRoomGuests = {
                    id: entity.id,
                    name: 'name' in entity ? entity.name  : 'Unknown',
                    username: entity.username,
                    avatar: entity.avatar || { filename: '', fileUrl: '' },
                    lastSeenAt: 'lastSeenAt' in entity ? entity.lastSeenAt : undefined,
                    status: 'status' in entity ? entity.status : 'Offline',
                    role: IPermissionEntityType.MEMBER, // Default role
                };

                // Check permissions to determine role
                const entityPermissions = permissions.filter(
                    (perm) => perm.entityId === entity.id && perm.isAllowed
                );

                if (entityPermissions.some((perm) => perm.permission === IRoomPermissionType.CAN_GRANT_ADMIN_ROLE && perm.entityType === IPermissionEntityType.ADMIN)) {
                    guest.role = IPermissionEntityType.ADMIN;
                    admins.push(guest);
                } else if (entityPermissions.some((perm) => perm.permission === IRoomPermissionType.CAN_GRANT_MODERATOR_ROLE && perm.entityType === IPermissionEntityType.MODERATOR)) {
                    guest.role = IPermissionEntityType.MODERATOR;
                    moderators.push(guest);
                } else {
                    guest.role = IPermissionEntityType.MEMBER;
                    members.push(guest);
                }
            });
            const messages = room.messages.map(Room.transformMessageToIMessage);

            // Return the transformed room details
            return {
                id: room.id,
                name: room.name,
                description: room.description || undefined,
                avatar: room.avatar || undefined,
                archived: room.archived,
                expiresAt: room.expiresAt || undefined,
                type: room.type as RoomType,
                membersCount: room.membersCount,
                onlineMembersCount: room.onlineMembersCount,
                createdAt: room.createdAt,
                updatedAt: room.updatedAt,
                setting: room.setting || undefined,
                metaData: room.metaData || undefined,
                guests: {
                admins,
                moderators,
                members,
                },
                messages,
            };
        } catch (error) {
            DBLogger.error('Error transforming room to IRoom, operationContext: Room.transformRoomToIRoom:', error);
            throw error;
        }
    }

   

    async createRoom(data: ICreateRoom, permissionSetter: Function): Promise<IRoom> {
        try {
        const validatedData = this.validateCreate(data);
        const { applicationId, creatorId, userIds = [], guestIds = [], tagIds = [], anonymousIds = [], ...rest } = validatedData;

        // Validate applicationId
        const application = await this.prisma.application.findUnique({ where: { id: applicationId } });
        if (!application) {
            throw new Error(`Application with ID ${applicationId} not found`);
        }

        // Validate userIds
        if (userIds.length > 0) {
            const users = await this.prisma.user.findMany({ where: { id: { in: userIds } } });
            if (users.length !== userIds.length) {
            throw new Error(`One or more user IDs not found: ${userIds.filter((id: string) => !users.some(u => u.id === id)).join(', ')}`);
            }
        }

        // Validate guestIds (including creatorId)
        const allGuestIds = [...new Set([...guestIds, creatorId])].filter(id => id != null);

        console.log(allGuestIds);
        if (allGuestIds.length > 0) {
            const guests = await this.prisma.guest.findMany({ where: { id: { in: allGuestIds } } });
            if (guests.length !== allGuestIds.length) {
            console.log(guests);
            throw new Error(`One or more guest IDs not found: ${allGuestIds.filter(id => !guests.some(g => g.id === id)).join(', ')}`);
            }
        }

        // Validate tagIds
        if (tagIds.length > 0) {
            const tags = await this.prisma.tag.findMany({ where: { id: { in: tagIds } } });
            if (tags.length !== tagIds.length) {
            throw new Error(`One or more tag IDs not found: ${tagIds.filter((id: string) => !tags.some(t => t.id === id)).join(', ')}`);
            }
        }

        // // Validate anonymousIds 
        if (anonymousIds.length > 0) {
            const anonymous = await this.prisma.anonymous.findMany({ where: { id: { in: anonymousIds } } });
            if (anonymous.length !== anonymousIds.length) {
            throw new Error(`One or more anonymous IDs not found: ${anonymousIds.filter((id: string) => !anonymous.some(a => a.id === id)).join(', ')}`);
            }
        }

        if(rest.avatar && rest.avatar.fileUrl){
            const createAvatar = await storage.downloadAndStoreWithAutoKey(
                rest.avatar.fileUrl,
                "sparkstrandchat/rooms", 
                {
                  cacheControl: 'public, max-age=2592000', // 30 days
                  metadata: {
                    uploadedBy: creatorId,
                    uploadType: 'avatar',
                    timestamp: new Date().toISOString()
                  }
                }
              );
            rest.avatar = {
                filename: createAvatar.key,
                fileUrl: createAvatar.url,
            };
        }

        const include = {
            messages: {
            include: {
                sender: true,
                files: {
                include: {
                    guest: true,
                },
                },
            },
            },
            guests: true,
            permissions: true,
        };

        // Create the room
        const room = await this.prisma.room.create({
            data: {
            ...rest,
            bannedGuestIds: [],
            membersCount: userIds.length + allGuestIds.length + anonymousIds.length,
            application: { connect: { id: applicationId } },
            ...(userIds.length ? {
                users: {
                connect: userIds.map((id: string) => ({ id })),
                },
            } : {}),
            ...(allGuestIds.length ? {
                guests: {
                connect: allGuestIds.map((id: string) => ({ id })),
                },
            } : {}),
            ...(tagIds.length ? {
                tags: {
                connect: tagIds.map((id: string) => ({ id })),
                },
            } : {}),
            ...(anonymousIds.length ? {
                anonymous: {
                connect: anonymousIds.map((id: string) => ({ id })),
                },
            } : {}),
            },
        });

        // Set permissions for group rooms
        if (room.type === RoomType.group) {
            await permissionSetter(room.id, creatorId);
        }

        // Fetch full room details
        const roomData = await this.prisma.room.findUnique({ where: { id: room.id }, include });

        if (!roomData) {
            throw new Error(`Failed to fetch created room with ID ${room.id}`);
        }

        return Room.transformRoomToIRoom(roomData);
        } catch (error: any) {
        DBLogger.error('Error creating room, operationContext: Room.createRoom:', error.message, error.stack);
        throw new Error(`Failed to create room: ${error.message}`);
        }
    }
      

    async updateRoom(id: string, data: IUpdateRoom): Promise<string | null> {
        try {
            this.validateId(id);
            const validatedData = this.validateUpdate(data);
            delete validatedData.id;
            const room = await this.prisma.room.update({
                where: { id },
                data: validatedData
            });
            return room?.id || null;
        } catch (error) {
            DBLogger.error('Error updating room, operationContext: Room.updateRoom :', error.message);
            if(error.code === 'P2025') {
                throw new Error('Not Found: Room not found');
            }
            throw error;
        }
    }

    async getRoomById(id: string): Promise<IRoom | null> {
        try {
            this.validateId(id);
            const room = await this.prisma.room.findUnique({
                where: { id },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                    },
                    guests: true,
                    permissions: true,
                },
            });
            return room ? Room.transformRoomToIRoom(room) : null;
        } catch (error) {
            DBLogger.error('Error getting room by ID, operationContext: Room.getRoomById:', error.message);
            throw error;
        }
    }

    async deleteRoom(id: string): Promise<void> {
        try {
            this.validateId(id);
            const room = await this.getRoomById(id);
            if (!room) {
                throw new Error('Not Found: Room not found');
            }
            await this.prisma.room.delete({
                where: { id }
            });
            return;
        } catch (error) {
            DBLogger.error('Error deleting room, operationContext: Room.deleteRoom:', error.message);
            throw error;
        }
    }

    async addUserToRoom(userId: string, roomId: string): Promise<number> {
        try {
            this.validateId(userId);
            this.validateId(roomId);
            const isAlreadyInRoom = await this.isGuestInRoom(userId, roomId);
            if (isAlreadyInRoom) {
                const room =  await this.prisma.room.findUnique({
                    where: {id: roomId},
                    select: { membersCount: true }
                })
                return room?.membersCount;
            }

            const room = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    users: {
                        connect: { id: userId }
                    },
                    membersCount: {
                        increment: 1
                    }
                },
                select: { membersCount: true }
            });
            return room?.membersCount || 0;
        } catch (error) {
            DBLogger.error('Error adding user to room, operationContext: Room.addUserToRoom:', error.message);
            throw error;
        }
    }

    async addGuestToRoom(guestId: string, roomId: string): Promise<number> {
        try {
            this.validateId(guestId);
            this.validateId(roomId);
            // check if guest and room exist or guest is already in the room;
            const isAlreadyInRoom = await this.isGuestInRoom(guestId, roomId);
            if (isAlreadyInRoom) {
                const room =  await this.prisma.room.findUnique({
                    where: {id: roomId},
                    select: { membersCount: true }
                })
                return room?.membersCount;
            }
            const room = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    guests: {
                        connect: { id: guestId }
                    },
                    membersCount: {
                        increment: 1
                    }
                },
                select: { membersCount: true }
            });
            return room?.membersCount || 0;
        } catch (error) {
            DBLogger.error('Error adding guest to room, operationContext: Room.addGuestToRoom:', error.message);
            if(error.code === 'P2025') {
                throw new Error('Not Found: Room or guest not found');
            }
            throw error;
        }
    }

    async addListOfGuestsToRoom(guestsId: string[], roomId: string): Promise<number> {
        try {
            this.validateId(roomId);
            // guestsId.forEach(this.validateId);
            const existingGuests = await this.prisma.room.findUnique({
                where: { id: roomId },
                select: { guests: true }
            });
            const newGuests = guestsId.filter(id => !existingGuests.guests.map(guest => guest.id).includes(id));
            if (newGuests.length === 0) {
                const room = await this.prisma.room.findUnique({
                    where: { id: roomId },
                    select: { membersCount: true }
                }) 
                return room.membersCount;
            }
            const room = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    guests: {
                        connect: newGuests.map(id => ({ id }))
                    },
                    membersCount: {
                        increment: newGuests.length
                    }
                },
                select: { membersCount: true }
            });
            return room?.membersCount || 0;
        } catch (error) {
            DBLogger.error('Error adding list of guests to room, operationContext: Room.addListOfGuestsToRoom:', error.message);
            throw error;
        }
    }

    /**
     * Remove a list of guests from a room
     * @param guestsId - List of guest IDs to remove
     * @param roomId - Room ID
     * @returns Number of members in the room after removal
     */
    async removeListOfGuestsFromRoom(guestsId: string[], roomId: string): Promise<number> {
        try {
            this.validateId(roomId);

            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                include: { guests: { select: { id: true } } }
            });

            // ✅ Only get guests that match the list of guests to remove
            const guestsToRemove = room.guests.filter(guest => guestsId.includes(guest.id));

            if (guestsToRemove.length === 0) {
                return room.membersCount; // Nothing to remove
            }

            const result = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    guests: {
                        disconnect: guestsToRemove.map(guest => ({ id: guest.id }))
                    },
                    membersCount: {
                        decrement: guestsToRemove.length
                    }
                },
                select: { membersCount: true }
            });
            // Clean up activity log for this guest
            await this.prisma.guestRoomActivity.deleteMany({
                where: {
                    guestId: {
                        in: guestsToRemove.map(guest => guest.id)
                    },
                    roomId
                }
            });
            
            return result?.membersCount || 0;
        } catch (error) {
            DBLogger.error(
                'Error removing list of guests from room, operationContext: Room.removeListOfGuestsFromRoom:',
                error.message
            );
            throw error;
        }
    }


    async getRoomGuests(roomId: string): Promise<IRoomGuests[]> {
        try {
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                select: {
                    guests: {
                        select: {
                            id: true,
                            name: true,
                            username: true,
                            avatar: true,
                            lastSeenAt: true,
                            status: true,
                        }
                    }
                }
            });
            if (!room) {
                throw new Error('Not Found: Room not found');
            }
            return room.guests;
        } catch (error) {
            DBLogger.error('Error getting room guests, operationContext: Room.getRoomGuests:', error.message);
            throw error;
        }
    }

    async isGuestInRoom(guestId: string, roomId: string): Promise<boolean> {
        try {
            this.validateId(guestId);
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                select: { guests: true }
            });
            if (!room) {
                throw new Error('Not Found: Room not found');
            }
            return room.guests.some(guest => guest.id === guestId);
        } catch (error) {
            DBLogger.error('Error checking if guest is in room: operationContext: Room.isGuestInRoom:', error.message);
            throw error;
        }
    }

    async addAnonymousToRoom(anonymousId: string, roomId: string): Promise<number> {
        try {
            this.validateId(anonymousId);
            this.validateId(roomId);
            const isAlreadyInRoom = await this.isGuestInRoom(anonymousId, roomId);
            if (isAlreadyInRoom) {
                const room =  await this.prisma.room.findUnique({
                    where: {id: roomId},
                    select: { membersCount: true }
                })
                return room?.membersCount;
            }
            const room = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    anonymous: {
                        connect: { id: anonymousId }
                    },
                    membersCount: {
                        increment: 1
                    }
                },
                select: { membersCount: true }
            });
            return room ? room.membersCount : 0;
        } catch (error) {
            DBLogger.error('Error adding anonymous user to room, operationContext: Room.addAnonymousToRoom:', error.message);
            throw error;
        }
    }

    async removeUserFromRoom(userId: string, roomId: string): Promise<number> {
        try {
            this.validateId(userId);
            this.validateId(roomId);
            const room = await this.prisma.room.findFirst({
                where: { id: roomId, userIds: { has: userId } },
                select: { userIds: true }
            });

            if (!room) {
                throw new Error('Not Found: Either the room does not exist or the user is not in the room');
            }

            const updatedUserIds = room.userIds.filter(id => id !== userId);

            const result = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    userIds: updatedUserIds,
                    membersCount: {
                        decrement: 1
                    }
                },
                select: { membersCount: true }
            });
            return result.membersCount;
        } catch (error) {
            DBLogger.error('Error removing user from room, operationContext: Room.removeUserFromRoom:', error.message);
            throw error;
        }
    }

    async removeGuestFromRoom(guestId: string, roomId: string): Promise<number> {
        try {
            this.validateId(guestId);
            this.validateId(roomId);

            const isGuestInRoom = await this.isGuestInRoom(guestId, roomId);
            if (!isGuestInRoom) {
                throw new Error('Member is not in the room');
            }

            const result = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    guests: {
                        disconnect: { id: guestId }
                    },
                    membersCount: {
                        decrement: 1
                    }
                },
                select: { membersCount: true }
            });

            // Clean up activity log for this guest
            await this.prisma.guestRoomActivity.delete({
                where: { guestId_roomId: { guestId, roomId } }
            });

            return result.membersCount;
        } catch (error) {
            DBLogger.error(
                'Error removing guest from room, operationContext: Room.removeGuestFromRoom:',
                error.message
            );
            throw error;
        }
    }


    async removeAnonymousFromRoom(anonymousId: string, roomId: string): Promise<number> {
        try {
            this.validateId(anonymousId);
            this.validateId(roomId);
            const room = await this.prisma.room.findFirst({
                where: { id: roomId, anonymousIds: { has: anonymousId } },
                select: { anonymousIds: true }
            });

            if (!room) {
                throw new Error('Not Found: Either the room does not exist or the anonymous user is not in the room');
            }

            const updatedAnonymousIds = room.anonymousIds.filter(id => id !== anonymousId);

            const result = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    anonymousIds: updatedAnonymousIds,
                    membersCount: {
                        decrement: 1
                    }
                },
                select: { membersCount: true }
            });
            return result.membersCount;
        } catch (error) {
            DBLogger.error('Error removing anonymous user from room, operationContext: Room.removeAnonymousFromRoom:', error.message);
            throw error;
        }
    }

    async updateOnlineMembersCount(roomId: string, count: number): Promise<number> {
        try {
            this.validateId(roomId);
            if (count < 0) {
                throw new Error('Online members count cannot be negative');
            }
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                select: {id: true}
            });
            if (!room) {
                throw new Error('Not Found: Room not found');
            }
            const result = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    onlineMembersCount: count
                },
                select: { onlineMembersCount: true }
            });
            return result.onlineMembersCount;
        } catch (error) {
            DBLogger.error('Error updating online members count: operationContext: Room.updateOnlineMembersCount:', error.message);
            throw error;
        }
    }

    async getRoomsByApplicationId(applicationId: string): Promise<IRoom[]> {
        try {
            this.validateId(applicationId);
            const rooms: RoomWithRelations[] = await this.prisma.room.findMany({
                where: { applicationId },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                    },
                    guests: true,
                    permissions: true,
                },
                orderBy: {
                    updatedAt: 'desc'
                },
                take: 25,
            });
            return rooms.map(Room.transformRoomToIRoom);
        } catch (error) {
            DBLogger.error('Error getting rooms by application ID, operationContext: Room.getRoomsByApplicationId:', error.message);
            throw error;
        }
    }

    async getRoomsByUserId(userId: string): Promise<IRoom[]> {
        try {
            this.validateId(userId);
            const result: RoomWithRelations[] =  await this.prisma.room.findMany({
                where: {
                    userIds: {
                        has: userId
                    }
                },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                    },
                    guests: true,
                    permissions: true,
                },
                orderBy: {
                    updatedAt: 'desc'
                },
                take: 25,
            });
            return result.map(Room.transformRoomToIRoom);
        } catch (error) {
            DBLogger.error('Error getting rooms by user ID, operationContext: Room.getRoomsByUserId:', error.message);
            throw error;
        }
    }

    async  getRoomsByGuestId(guestId: string): Promise<IRoom[]> {
        try {
            const rooms: RoomWithRelations[] = await this.prisma.room.findMany({
                where: {
                    guestIds: { has: guestId},
                },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: { guest: true },
                            },
                        },
                    },
                    guests: true,
                    permissions: true,
                },
            });
            return rooms.map(Room.transformRoomToIRoom);
        } catch (error: any) {
            DBLogger.error('Error getting rooms by guest ID, operationContext: Room.getRoomsByGuestId:', error.message);
            throw error;
        }
    }

    /**
     * Get all the messages that the guest has missed in the room
     * Return messages from oldest to newest
     * @param guestId 
     * @param roomId 
     * @returns 
     */
    async getGuestMissedMessages(guestId: string, roomId: string): Promise<IMessage[]> {
        try {
            this.validateId(guestId);
            this.validateId(roomId);
            const guestRoomActivity = await this.prisma.guestRoomActivity.findUnique({
                where: { guestId_roomId: { guestId, roomId } }
            });
            if(!guestRoomActivity) {
                return [];
            }
            const { lastReadMessageId, lastReadAt }= guestRoomActivity;
            let result: MessageWithRelations[] = [];
            let whereClause: any = {
                to: roomId,
                senderId: {
                    not: guestId
                }
            };
            if(lastReadMessageId) {
                whereClause.id = { gt: lastReadMessageId };
            } else {
                whereClause.createdAt = { gt: lastReadAt };
            }
            result = await this.prisma.message.findMany({
                where: whereClause,
                include: {
                    files: {
                        include: {
                            guest: true,
                        },
                    },
                    sender: true,
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
            return result.map(Room.transformMessageToIMessage).reverse();
        } catch (error) {
            DBLogger.error('Error getting guest missed messages, operationContext: Room.getGuestMissedMessages:', error.message);
            throw error;
        }
    }

      /**
     * Get a Guest last Read Message Id and the read timestamp
     */
    async getGuestLastReadMessage(guestId: string, roomId: string): Promise<{ lastReadMessageId?: string, lastReadAt?: Date }> {
        try {
            this.validateId(guestId);
            this.validateId(roomId);
            const guestRoomActivity = await this.prisma.guestRoomActivity.findUnique({
                where: { guestId_roomId: { guestId, roomId } }
            });
            if(!guestRoomActivity) {
                return { lastReadMessageId: null, lastReadAt: null };
            }
            return { lastReadMessageId: guestRoomActivity.lastReadMessageId, lastReadAt: guestRoomActivity.lastReadAt };
        } catch (error) {
            DBLogger.error('Error getting guest last read message, operationContext: Room.getGuestLastReadMessage:', error.message);
            throw error;
        }
    }

    async getRoomsByGuestIdAndApplicationId(guestId: string, applicationId: string): Promise<IRoom[]> {
        try {
            this.validateId(guestId);
            this.validateId(applicationId);
            const result: RoomWithRelations[] = await this.prisma.room.findMany({
                where: {
                    guestIds: {
                        has: guestId
                    },
                    applicationId,
                },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                    },
                    guests: true,
                    permissions: true,
                },
            });
                
            return result.map(Room.transformRoomToIRoom);
        } catch (error) {
            DBLogger.error('Error getting rooms by guest ID and application ID, operationContext: Room.getRoomsByGuestIdAndApplicationId:', error.message);
            throw error;
        }
    }

    async getRoomsByGuestIdAndAppIdAndCompanyId(guestId: string, applicationId: string, companyId: string): Promise<IRoom[]> {
        try {
            this.validateId(guestId);
            this.validateId(applicationId);
            this.validateId(companyId);
            const result: RoomWithRelations[] =  await this.prisma.room.findMany({
                where: {
                    guestIds: {
                        has: guestId
                    },
                    application: {
                      is: {
                        id: applicationId,
                        companyId
                      }
                    },
                  },
                  include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                    },
                    guests: true,
                    permissions: true,
                },
            });
            const rooms = result.map(Room.transformRoomToIRoom);
            return rooms;
        } catch (error) {
            DBLogger.error('Error getting rooms by guest ID and application ID and company ID, operationContext: Room.getRoomsByGuestIdAndAppIdAndCompanyId:', error.message);
            throw error;
        }
    }

    async getRoomsByAnonymousId(anonymousId: string): Promise<IRoom[]> {
        try {
            this.validateId(anonymousId);
            const result: RoomWithRelations[] = await this.prisma.room.findMany({
                where: {
                    anonymousIds: {
                        has: anonymousId
                    },
                },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                        
                    },
                    guests: true,
                    permissions: true,
                },
            })
            return result.map(Room.transformRoomToIRoom);
        } catch (error) {
            DBLogger.error('Error getting rooms by anonymous ID, operationContext: Room.getRoomsByAnonymousId:', error.message);
            throw error;
        }
    }

    async addTagToRoom(tagId: string, roomId: string): Promise<string> {
        try {
            this.validateId(tagId);
            this.validateId(roomId);
            await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    tags: {
                        connect: { id: tagId }
                    }
                }
            });
            return tagId;
        } catch (error) {
            DBLogger.error('Error adding tag to room, operationContext: Room.addTagToRoom:', error.message);
            throw error;
        }
    }

    async removeTagFromRoom(tagId: string, roomId: string): Promise<string> {
        try {
            this.validateId(tagId);
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                select: { tags: true }
            });

            if (!room) {
                throw new Error('Not Found: Room not found');
            }

            const updatedTagIds = room.tags.filter(tag => tag.id !== tagId);

            await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    tags: {
                        disconnect: updatedTagIds.map(tag => ({ id: tag.id }))
                    }
                }
            }) 
            return tagId;
        } catch (error) {
            DBLogger.error('Error removing tag from room, operationContext: Room.removeTagFromRoom:', error.message);
            throw error;
        }
    }

    async findFirstRecord(query: any): Promise<any> {
        try {
            return await this.prisma.room.findFirst({ ...query});
        } catch (error) {
            DBLogger.error('Error findFirstRecord room, operationContext: Room.findFirstRecord:', error.message);
            throw error;
        }
    }

    async findManyRecord(query: any): Promise<any[]> {
        try {
            return await this.prisma.room.findMany({...query});
        } catch (error) {
            DBLogger.error('Error findManyRecord room, operationContext: Room.findManyRecord:', error.message);
            throw error;
        }
    }

    async updateRoomMetaData(roomId: string, data: any): Promise<any> {
        try {
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                select: { metaData: true }
            });
            if(!room) {
                throw new Error('Not Found: Room not found');
            }
            const oldMetaData = room?.metaData ? JSON.parse(room.metaData as any) : {};
            const updatedRoom = await this.prisma.room.update({
                where: { id: roomId },
                data: {
                    metaData: JSON.stringify({ ...oldMetaData, ...data })
                }
            });
            return updatedRoom.metaData ? JSON.parse(updatedRoom.metaData as any) : {};
        } catch (error) {
            DBLogger.error('Error updating room metadata, operationContext: Room.updateRoomMetaData:', error.message);
            throw error;
        }
    }

    async getRoomMetaData(roomId: string): Promise<any> {
        try {
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                select: { metaData: true }
            });
            if(!room) {
                throw new Error('Not Found: Room not found');
            }
            return room?.metaData ? JSON.parse(room.metaData as any) : {};
        } catch (error) {
            DBLogger.error('Error getting room metadata operationContext: Room.getRoomMetaData:', error.message);
            throw error;
        }
    }

    async getRoomFullDetails(roomId: string,): Promise<IRoom> {
        try {
            this.validateId(roomId);
            const room: RoomWithRelations | null = await this.prisma.room.findUnique({
            where: { id: roomId },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                    },
                    guests: true,
                    permissions: true,
                },
            });
        
            if(!room) {
                throw new Error(`Not Found: Room with ID ${roomId} not found`);
            }

            return Room.transformRoomToIRoom(room);
        } catch (error) {
            DBLogger.error('Error getting room full details, operationContext: Room.getRoomFullDetails:', error.message);
            throw error;
        }
    }


    async getRoomMedia(roomId: string): Promise<IRoomMedia[]> {
        try {
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                include: {
                    files: {
                        include: {
                            guest: true,
                        },
                    },
                },
            });
            if(!room) {
                throw new Error('Not Found: Room not found');
            }
            const media: IRoomMedia[] = room.files.map((file) => {
                const { guest } = file;
                return { 
                    uploader: { id: guest?.id, name: guest?.name || 'Unknown', username: guest?.username || 'unknown' },
                    id: file.id,
                    filename: file.filename,
                    fileUrl: file.fileUrl,
                    fileType: file.fileType,
                    size: file.size,
                    createdAt: file.createdAt,
                    messageId: file.messageId,
                };
            });
            return media;
        } catch (error) {
            DBLogger.error('Error getting room media, operationContext: Room.getRoomMedia:', error.message);
            throw error;
        }
    }

    async getRoomMessages(roomId: string, limit?: number , cursor?: string): Promise<IMessage[]> {
        try {
            const page = {
                take: limit || 100,
                cursor: cursor ? { id:  cursor } : undefined,
            };
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                        ...page
                    }
                },
            });
            if(!room) {
                throw new Error('Not Found: Room not found');
            }
            return room.messages.map(Room.transformMessageToIMessage);
        } catch (error) {
            DBLogger.error('Error getting room messages, operationContext: Room.getRoomMessages:', error.message);
            throw error;
        }
    }

    /**
     * Get Room Last Message
     */
    async getRoomLastMessage(roomId: string): Promise<IMessage | null> {
        try {
            this.validateId(roomId);
            const room = await this.prisma.room.findUnique({
                where: { id: roomId },
                include: {
                    messages: {
                        include: {
                            sender: true,
                            files: {
                                include: {
                                    guest: true,
                                },
                            },
                        },
                        orderBy: {
                            createdAt: 'desc'
                        },
                        take: 1
                    }
                },
            });
            if(!room) {
                throw new Error('Not Found: Room not found');
            }
            return room.messages.length > 0 ? Room.transformMessageToIMessage(room.messages[0]) : null;
        } catch (error) {
            DBLogger.error('Error getting room last message, operationContext: Room.getRoomLastMessage:', error.message);
            throw error;
        }
    }
}

