// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

type Avatar {
  filename String
  fileUrl  String
}

type Location {
  country   String
  region    String
  city      String
  longitude String?
  latitude  String?
}

// #region USER
///  A `User` can either own an account or not, but can own just one account at a time.
/// One `User` can have access to many companies in any `Account`.
model User {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  displayName     String
  name            String
  username        String
  email           String    @unique
  password        String
  awayModeEnabled Boolean   @default(false)
  avatar          Avatar?
  lastSeenAt      DateTime  @updatedAt
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  location        Location?

  /// If a user owns an account, then they are an owner and this field is set to `Owner`.
  /// Then the accountId and account fields are set to the account they own.
  /// The account owner has the highest level of access in an account.
  role      OwnerRole @default(User)
  accountId String?   @db.ObjectId
  account   Account?

  /// A user can belong to many companies and a company can have many users
  /// See the `Company` model
  companyIds String[]  @db.ObjectId
  companies  Company[] @relation(fields: [companyIds], references: [id])

  roomIds String[] @db.ObjectId
  rooms   Room[]   @relation(fields: [roomIds], references: [id])

  analyticId String     @db.ObjectId
  analytic   Analytic   @relation(fields: [analyticId], references: [id])
  File       File[]
  userRoles  UserRole[]
}

enum OwnerRole {
  Owner
  User
}

// #region ACCOUNT
model Account {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  name              String
  companiesCount    Int      @default(0)
  applicationsCount Int      @default(0)
  usersCount        Int      @default(0)
  guestsCount       Int      @default(0)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  companies         Company[]
  accountSubscriptions AccountSubscription[]

  ownerId           String @unique @db.ObjectId
  owner             User   @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  accessKeys        AccessKey[]
}

// #region COMPANY
model Company {
  id                String    @id @default(auto()) @map("_id") @db.ObjectId
  name              String    @unique
  website           String?
  industry          String?
  domains           String[]  // both Development and production domains
  applicationsCount Int       @default(0)
  usersCount        Int       @default(0)
  guestsCount       Int       @default(0)
  monthlySpend      Int?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  location          Location?   


  providers         CloudStorageProvider[]

  tagIds            String[] @db.ObjectId
  tags              Tag[]    @relation(fields: [tagIds], references: [id])

  accountId String  @db.ObjectId
  account   Account @relation(fields: [accountId], references: [id], onDelete: Cascade)

  // Many-to-Many relation between `Company` and `User`
  userIds String[] @db.ObjectId
  users   User[]   @relation(fields: [userIds], references: [id])

  // Many-to-Many relation between `Company` and `User`
  guestIds String[] @db.ObjectId
  guests   Guest[]  @relation(fields: [guestIds], references: [id])

  anonymousIds    String[]    @db.ObjectId
  anonymousGuests Anonymous[] @relation(fields: [anonymousIds], references: [id])

  applications Application[]

  /// A `Company` can create one or more roles for its users.
  /// A role determines a user's access levels or permissions.
  roles Role[]

  /// Roles of users in a company.
  userRoles UserRole[]

  /// accessKeys are what grant access to a company and applications in the company.
  accessKeys AccessKey[]
}


// #region ROLE
model Role {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  companyId String     @db.ObjectId
  company   Company    @relation(fields: [companyId], references: [id], onDelete: Cascade)
  userRoles UserRole[]
}

// #region USER ROLE
/// These are roles that determine a user's access levels or permissions.
/// They are preset by the `Company`. A user can have one role at a time in a company.
model UserRole {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  companyId String  @db.ObjectId
  company   Company @relation(fields: [companyId], references: [id])

  userId String @db.ObjectId
  user   User   @relation(fields: [userId], references: [id])

  roleId String @db.ObjectId
  role   Role   @relation(fields: [roleId], references: [id])

  @@unique ([companyId, userId])
  @@unique([companyId, userId, roleId])
}

// #region APPLICATION
model Application {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  name          String  
  usersCount    Int      @default(0)
  guestsCount   Int      @default(0)
  lastRequestAt DateTime @updatedAt
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  type          ApplicationType @default(General)
  setting       Json?

  companyId String  @db.ObjectId
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  /// A guest can belong to many applications and an application can have many guests
  /// See the `Guest` model
  guestIds String[] @db.ObjectId
  guests   Guest[]  @relation(fields: [guestIds], references: [id])

  anonymousIds    String[]    @db.ObjectId
  anonymousGuests Anonymous[] @relation(fields: [anonymousIds], references: [id])

  rooms Room[]

  @@unique([companyId, name])
  @@unique([companyId, type])
  @@unique([id, type])
}

enum ApplicationType {
  General
  Customer_Support
  Education
  Team_Collaboration
  Healthcare_Teleconsultation
  Ecommerce_Live_Shopping
  Community
  Notification
}

// #region ACCESS KEYS
/// An access key is what grants access to an application.
/// They are unique to every client in an application.
model AccessKey {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String    
  apiKey        String    @unique
  apiKeySecret  String    @unique
  status        KeyStatus @default(Active)
  expiresAt     DateTime
  lastRequestAt DateTime  @updatedAt
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  creatorId String  @db.ObjectId
  creator   Account @relation(fields: [creatorId], references: [id])

  companyId String  @db.ObjectId
  Company   Company @relation(fields: [companyId], references: [id])
  @@unique([apiKey, companyId])
  @@unique([apiKey, creatorId])
  @@unique([companyId, name])
}

enum KeyStatus {
  Active
  Revoked
  Expired
}

// #region GUEST
model Guest {
  id                String    @id @default(auto()) @map("_id") @db.ObjectId
  displayName       String?
  name              String
  username          String
  externalId        String    @unique
  email             String?
  phone             String?   
  awayModeEnabled   Boolean   @default(false)
  avatar            Avatar?
  location          Location?
  lastSeenAt        DateTime  @updatedAt
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  currentRoomId     String?    @db.ObjectId
  status            GuestStatus @default(Online)
  // Additional analytics fields
  hasHardBounced    Boolean   @default(false)
  markedEmailAsSpam Boolean   @default(false)


  applicationIds String[]      @db.ObjectId
  application    Application[] @relation(fields: [applicationIds], references: [id])

  companyIds String[]  @db.ObjectId
  companies  Company[] @relation(fields: [companyIds], references: [id])

  roomIds String[] @db.ObjectId
  rooms   Room[]   @relation(fields: [roomIds], references: [id])

  messages Message[]

  analyticId  String   @db.ObjectId
  analytic   Analytic @relation(fields: [analyticId], references: [id])
  File       File[]

}

enum GuestStatus {
  Online
  Away
  Offline
}

// #region ANONYMOUS
model Anonymous {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  displayName     String    @default("anon")
  email           String?
  phone           String?   
  anonymous       Boolean   @default(false)
  awayModeEnabled Boolean   @default(false)
  avatar          Avatar?
  location        Location?
  lastSeenAt      DateTime  @updatedAt
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  applicationIds String[]      @db.ObjectId
  application    Application[] @relation(fields: [applicationIds], references: [id])

  companyIds String[]  @db.ObjectId
  companies  Company[] @relation(fields: [companyIds], references: [id])

  roomIds String[] @db.ObjectId
  rooms   Room[]   @relation(fields: [roomIds], references: [id])
}

// #region MESSAGE
model Message {
  id        String        @id @default(auto()) @map("_id") @db.ObjectId
  /// Can be nullable as a chat may not necessarily contain text, just images or files
  text       String?
  isPinned   Boolean @default(false)
  isAnswered Boolean @default(false)
  isEncrypted Boolean @default(false)
  status    MessageStatus @default(Sent)
  parentId  String?       @db.ObjectId
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  read      Boolean       @default(false)
  senderId  String        @db.ObjectId
  edited    Boolean       @default(false)
  /// Recipient of the message is always a room.
  to        String        @db.ObjectId
  files     File[]

  room      Room   @relation(fields: [to], references: [id], onDelete: Cascade)
  sender    Guest  @relation(fields: [senderId], references: [id], onDelete: Cascade)

  analyticId String?   @db.ObjectId
  analytic   Analytic? @relation(fields: [analyticId], references: [id])
}

enum MessageStatus {
  Sent
  Delivered
  Read
}
enum MessageSenderType {
  /// admin and chatAgents are users who have higher level access in a company
  admin
  chatAgent
  aiAgent
  guest
}

type MessageSender {
  id   String            @db.ObjectId
  type MessageSenderType
}

// #region ROOM
model Room {
  id                 String   @id @default(auto()) @map("_id") @db.ObjectId
  name               String
  description        String?
  avatar             Avatar?
  archived           Boolean  @default(false)
  membersCount       Int      @default(0)
  onlineMembersCount Int      @default(0)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  type               RoomType @default(dm)
  setting            Json?
  metaData           Json? // Additional data for the room
  bannedGuestIds     String[] @db.ObjectId
  expiresAt          DateTime?

  permissions        RoomPermission[]

  applicationId String      @db.ObjectId
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  messages Message[]

  userIds String[] @db.ObjectId
  users   User[]   @relation(fields: [userIds], references: [id])

  guestIds String[] @db.ObjectId
  guests   Guest[]  @relation(fields: [guestIds], references: [id])

  tagIds String[] @db.ObjectId
  tags   Tag[]    @relation(fields: [tagIds], references: [id])

  files File[]

  anonymous    Anonymous[] @relation(fields: [anonymousIds], references: [id])
  anonymousIds String[]    @db.ObjectId
}

// #region GUEST_ROOM_ACTIVITY - Tracks the last read message for a guest in a room and other activity
model GuestRoomActivity {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  guestId        String   @db.ObjectId
  roomId         String   @db.ObjectId
  lastReadMessageId String? @db.ObjectId
  lastReadAt     DateTime? @updatedAt

  @@unique([guestId, roomId])
}


enum RoomType {
  dm
  group
  self
  anonymous
}

// #region TAG
model Tag {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  roomIds String[] @db.ObjectId
  rooms   Room[]   @relation(fields: [roomIds], references: [id])

  companyIds String[]  @db.ObjectId
  companies  Company[] @relation(fields: [companyIds], references: [id])
}

// #region ANALYTIC
model Analytic {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  language        String?
  browser         String?
  browserVersion  String?
  browserLanguage String?
  os              String?
  ipAddress       String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  users    User[]
  guests   Guest[]
  messages Message[]
}


enum CloudProviderType {
  AWS
  GCP
  AZURE
}

model CloudStorageProvider {
  id               String                 @id @default(auto()) @map("_id") @db.ObjectId
  name             String
  provider         CloudProviderType
  bucketName       String?
  containerName    String?
  region           String?
  projectId        String?
  accountName      String?
  isActive         Boolean                @default(true)

  companyId        String                 @db.ObjectId
  company          Company                @relation(fields: [companyId], references: [id], onDelete: Cascade)

  keyId            String?
  secretKey        String?
  credentialsJson  String?
  accountKey       String?
  connectionString String?
  description      String?
  isPrimary        Boolean                @default(false)
  isRevoked        Boolean                @default(false)

  files            File[]

  createdAt        DateTime               @default(now())
  updatedAt        DateTime               @updatedAt

  /// No two providers can have the same combination of isActive, isPrimary, and isRevoked values. 
  /// For example, if a company has an AWS provider with isActive: true, isPrimary: true, and isRevoked: false, no other AWS provider for that company can have this exact combination of boolean values.
  @@unique([companyId, provider, isActive, isPrimary, isRevoked])
}

//#region Files
model File {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  key       String   // e.g. "uploads/company_name/userId/media/uuid-filename.pdf"
  filename  String
  fileUrl   String
  fileType  String?
  size      Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  uploadedBy String @db.ObjectId
  messageId  String? @db.ObjectId
  roomId     String? @db.ObjectId

  providerId     String?       @db.ObjectId
  provider       CloudStorageProvider? @relation(fields: [providerId], references: [id])

  user    User?   @relation(fields: [uploadedBy], references: [id], onDelete: Cascade)
  guest   Guest?  @relation(fields: [uploadedBy], references: [id], onDelete: Cascade)
  message Message? @relation(fields: [messageId], references: [id], onDelete: Cascade)
  room    Room?    @relation(fields: [roomId], references: [id], onDelete: Cascade)
}

//#region SUBSCRIPTION
model Subscription {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  plan        SubscriptionPlan @default(Free)
  price       Int
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  accountSubscriptions AccountSubscription[]
}

enum SubscriptionPlan {
  Free
  Basic
  Standard
  Premium
  Enterprise
}


/// Tracks an account's subscription lifetime
model AccountSubscription {
  id        String    @id @default(auto()) @map("_id") @db.ObjectId
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  endedAt   DateTime?
  active    Boolean   @default(false)

  accountId String  @db.ObjectId
  account   Account @relation(fields: [accountId], references: [id], onDelete: Cascade)

  subscriptionId String       @db.ObjectId
  subscription   Subscription @relation(fields: [subscriptionId], references: [id])
}

// #region TOKEN
model Token {
  id          String          @id @default(auto()) @map("_id")   @db.ObjectId
  type        TokenType
  token       String          @unique
  externalId  String?
  isDelete    Boolean         @default(false)
  isRevoke    Boolean         @default(false)
  isExpired   Boolean         @default(false)
  metaData    Json?
  deleteAt    DateTime?
  revokeAt    DateTime?
  expiresAt   DateTime?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  @@index([expiresAt])
  @@index([type])
}

enum TokenType {
  ACCESS
  REFRESH
  INVITATION
  VERIFICATION
}


/// What kinds of actions can be allowed or denied in a room
enum RoomPermissionType {
  // Default Room permissions
  CAN_SEND_MESSAGES             // can post new messages,
  CAN_DELETE_OWN_MESSAGES       // can delete their own messages,
  CAN_EDIT_OWN_MESSAGES         // can edit their own messages,
  CAN_UPLOAD_FILES             // can upload files,
  CAN_DELETE_OWN_FILES         // can delete their own files,

  // Moderator permissions
  CAN_DELETE_OTHER_MESSAGES     // can delete other people's messages,
  CAN_PIN_MESSAGES              // can pin/unpin messages,
  CAN_MODERATE_USERS            // can mute/kick/ban users,
  CAN_CHANGE_ROOM_NAME          // can change room name,
  CAN_CHANGE_ROOM_DESCRIPTION   // can change room description,
  
  // Admin permissions
  CAN_MANAGE_PERMISSIONS         // can manage room permissions,
  CAN_MANAGE_SETTINGS            // can manage room settings,
  CAN_GRANT_MODERATOR_ROLE       // can grant moderator role,
  CAN_UPDATE_ROOM_DATA           // can update room details,
  CAN_GRANT_ADMIN_ROLE           // can grant admin role
}

/// Which “member” the permission applies to

enum PermissionEntityType {
  ADMIN        
  MODERATOR    
  MEMBER
  // A Role that a company defines (entityType = ROLE, entityId = Role.id)
  // So one can grant e.g. all “Support Agents” the ability to pin messages.
  ROLE        // a Company Role.id (via UserRole → Role) 
  ANONYMOUS   // 
}

/// A single allow/deny rule for a given room + member + action

//#region ROOM PERMISSION
model RoomPermission {
  id           String                 @id @default(auto()) @map("_id") @db.ObjectId
  roomId       String                 @db.ObjectId
  room         Room                   @relation(fields: [roomId], references: [id], onDelete: Cascade)

  entityType   PermissionEntityType
  entityId     String                // e.g. userId, guestId, roleId or anonymousId or special marker 'ALL' (for all members)

  permission   RoomPermissionType
  isAllowed    Boolean                @default(true)

  createdAt    DateTime               @default(now())
  updatedAt    DateTime               @updatedAt

  @@unique([roomId, entityType, entityId, permission])
}


