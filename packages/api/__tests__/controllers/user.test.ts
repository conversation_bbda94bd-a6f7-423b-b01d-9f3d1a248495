import { NextFunction, Request, Response } from 'express';
import { UserController } from '../../v1/controllers';
import { UserService } from '../../v1/services';
import { Api<PERSON>rror, ControllerHelper, IRequest, IResponse, Role } from '../../utils';
import * as USER from '../../__mocks__/userMock';
import { IDelete, IUserGeneralData, User } from '../../models';

jest.mock('../../v1/services/user.service');
jest.mock('../../utils/controllerHelpers');



describe('UserController', () => {
  let userController: UserController;
  let mockUserService: jest.Mocked<UserService>;
  let mockControllerHelper: jest.Mocked<ControllerHelper>;
  let mockRequest: Partial<IRequest>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock<NextFunction>;
  let mockBasePayload: {type: Role.USER, iat: number, exp: number, isAuthenticated: boolean, id?: string };

  beforeEach(() => {
    jest.clearAllMocks();

    const controllerHelper = new ControllerHelper();
    const userService = new UserService(new User());

    mockUserService = {
      Create: jest.fn(userService.Create),
      Update: jest.fn(userService.Update),
      Delete: jest.fn(userService.Delete),
      Login: jest.fn(userService.Login),
      ChangePassword: jest.fn(userService.ChangePassword),
    } as any;

    mockControllerHelper = {
      validateRequestBody: jest.fn(controllerHelper.validateRequestBody),
      handleServiceResult: jest.fn(controllerHelper.handleServiceResult),
      handleServiceResultWithCookie: jest.fn(controllerHelper.handleServiceResultWithCookie),
      validateUser: jest.fn(controllerHelper.validateUser),
      validateParameterId: jest.fn(controllerHelper.validateParameterId),
      validateQueryParams: jest.fn(controllerHelper.validateQueryParams),
      clearCookie: jest.fn(controllerHelper.clearCookie),
    } as any;

    userController = new UserController(
      mockUserService as UserService,
      mockControllerHelper as ControllerHelper
    );

    mockBasePayload = { type: Role.USER, iat: 123456789, exp: 123456789, isAuthenticated: true };

    mockRequest = {
      params: {},
      query: {},
      user: undefined,
      headers: { ...USER.MockHeader },
      ip: USER.MockIp,
      socket: { ...USER.MockSocket } as any,
      files: USER.MockFiles,
    };

    mockResponse = {
      statusCode: 200,
      json: jest.fn(),
    };

    mockNext = jest.fn();
  });

  describe('.Create', () => {
    const analyticData = USER.MockAnalytic;
    const avatar = USER.MockAvatar;
    let resultDataFromUserModel = USER.MockUserGeneralData;
    let resultFromUserservice = { success: true, data: resultDataFromUserModel, message: 'User created successfully', statusCode: 201 };
    mockRequest = { ...mockRequest, body: { ...USER.MockCreateUserData } };

    it('Should create user successfully', async () => {
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockUserService.Create.mockResolvedValue(resultFromUserservice);

      await userController.Create(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockUserService.Create).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockRequest.body, avatar }),
        expect.objectContaining(analyticData)
      );

      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(
        mockRequest,
        'Create user controller'
      );

      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        resultFromUserservice,
        mockResponse,
        'Create user controller'
      );

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle required fields validation errors during user creation e.g email', async () => {
      resultFromUserservice = {
        success: false,
        data: null,
        message: 'Validation failed, Required : email',
        statusCode: 400,
      };

      mockRequest = { ...mockRequest, body: { ...mockRequest.body, email: undefined } };

      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockUserService.Create.mockResolvedValue(resultFromUserservice);

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await userController.Create(mockRequest as IRequest, mockResponse as Response, mockNext as NextFunction);

      expect(mockUserService.Create).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockRequest.body, avatar }),
        expect.objectContaining(analyticData)
      );

      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(
        mockRequest,
        'Create user controller'
      );

      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        resultFromUserservice,
        mockResponse,
        'Create user controller'
      );

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(
        new ApiError(resultFromUserservice.message, resultFromUserservice.statusCode, 'Create user controller')
      );
    });
  });

  describe('.Delete', () => {
    let mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1234', email: '<EMAIL>',  };
    let resultFromUserservice: IResponse<null> = { success: true, data: null, message: 'User deleted successfully', statusCode: 200 };
    mockRequest = { ...mockRequest, body: undefined, user: mockRequestDotUserObject };
    mockResponse = { ...mockResponse, req: { cookies: { token: 'somerandomcharactershardtodecipher' } }, clearCookie: jest.fn() } as unknown as Response;

    it('should delete user successfully', async () => {
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockUserService.Delete.mockResolvedValue(resultFromUserservice);

      await userController.Delete(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Delete user controller');
      expect(mockUserService.Delete).toHaveBeenCalledWith({ id: mockRequestDotUserObject.id });
      expect(mockControllerHelper.clearCookie).toHaveBeenCalledWith(mockResponse);
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        resultFromUserservice,
        mockResponse,
        'Delete user controller'
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors during user deletion', async () => {
      mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d', email: '<EMAIL>' };

      mockRequest = { ...mockRequest, body: undefined, user: mockRequestDotUserObject };

      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);

      await userController.Delete(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockUserService.Delete).toHaveBeenCalledWith({ id: mockRequestDotUserObject.id });
      expect(mockControllerHelper.clearCookie).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('.Update', () => {
    let mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1234', email: '<EMAIL>',  };
    mockRequest = { ...mockRequest, body: { displayName: 'Updated Name' }, user: mockRequestDotUserObject };
    const avatar = USER.MockAvatar;
    let resultFromUserModel = { ...USER.MockUserGeneralData, id: mockRequestDotUserObject.id };
    let resultFromUserService: IResponse<IUserGeneralData> = { success: true, data: resultFromUserModel, message: 'Data updated successfully', statusCode: 200 };

    it('should update user successfully', async () => {
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);

      mockUserService.Update.mockResolvedValue(resultFromUserService);

      await userController.Update(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalled();
      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Update user controller');
      expect(mockUserService.Update).toHaveBeenCalledWith(expect.objectContaining({ ...mockRequest.body, id: mockRequestDotUserObject.id, avatar }));
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromUserService, mockResponse, 'Update user controller');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error when request body has password field during user update', async () => {
      mockRequest = { ...mockRequest, body: { password: 'UpdatedPassword' }, user: mockRequestDotUserObject };
      const errorObject = {
        success: false,
        statusCode: 400,
        data: null as IDelete,
        message: 'Password update is not allowed on this endpoint, please use the reset password endpoint'
      };

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await userController.Update(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateRequestBody).not.toHaveBeenCalled();
      expect(mockControllerHelper.validateUser).not.toHaveBeenCalled();
      expect(mockUserService.Update).not.toHaveBeenCalled();
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(errorObject, mockResponse, 'Update user controller');

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError(errorObject.message, errorObject.statusCode, 'Update user controller'));
    });
  });

  describe('.Login', () => {
    mockRequest = { ...mockRequest, body: USER.MockUserLoginData, user: undefined };
    const data = { data: USER.MockUserGeneralData, token: 'somerandomcharactershardtodecipher' };
    let resultFromUserService: IResponse<{ token: string, data: IUserGeneralData } | null> = { success: true, data: data, message: 'Logged in successfully', statusCode: 200 };

    it('should login user successfully', async () => {
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockUserService.Login.mockResolvedValue(resultFromUserService);

      await userController.Login(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Login user controller');
      expect(mockUserService.Login).toHaveBeenCalledWith(mockRequest.body);
      expect(mockControllerHelper.handleServiceResultWithCookie).toHaveBeenCalledWith(resultFromUserService, mockResponse, false, 'Login user controller');

      expect(mockControllerHelper.handleServiceResult).not.toHaveBeenCalled();
      expect(mockControllerHelper.validateUser).not.toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle login errors', async () => {
      mockRequest = { ...mockRequest, body: { email: 'badgmail.com', password: USER.MockUserLoginData.password } };
      resultFromUserService = { success: false, data: null, message: 'Invalid email or password', statusCode: 400 };

      mockUserService.Login.mockResolvedValue(resultFromUserService);
      mockControllerHelper.handleServiceResultWithCookie.mockImplementation((result, response, isSDk, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await userController.Login(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalled();
      expect(mockUserService.Login).toHaveBeenCalledWith(mockRequest.body);
      expect(mockControllerHelper.handleServiceResultWithCookie).toHaveBeenCalledWith(resultFromUserService, mockResponse, false, 'Login user controller');
      expect(mockControllerHelper.handleServiceResult).not.toHaveBeenCalled();
      expect(mockControllerHelper.validateUser).not.toHaveBeenCalled();

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError(resultFromUserService.message, resultFromUserService.statusCode, 'Login user controller'));
    });
  });

  describe('.Logout', () => {
    mockRequest = { ...mockRequest, user: { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1234', email: '<EMAIL>' } };

    it('should logout user successfully', async () => {
      mockControllerHelper.validateUser.mockReturnValue(mockRequest.user);

      await userController.Logout(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Logout user controller');
      expect(mockControllerHelper.clearCookie).toHaveBeenCalledWith(mockResponse, false);
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        { success: true, statusCode: 200, data: null, message: 'Logged out successfully' },
        mockResponse,
        'Logout user controller'
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors during user logout', async () => {
      mockRequest = { ...mockRequest, user: undefined };

      mockControllerHelper.validateUser.mockImplementation((request, controllerName) => {
        if (!request?.user) {
          throw new ApiError('Unauthorized', 401, controllerName);
        }
        return request.user;
      });

      await userController.Logout(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Logout user controller');
      expect(mockControllerHelper.clearCookie).not.toHaveBeenCalled();
      expect(mockControllerHelper.handleServiceResult).not.toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError('Unauthorized', 401, 'Logout user controller'));
    });
  });

  describe('.ChangePassword', () => {
    let mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1234', email: '<EMAIL>',  };
    mockRequest = { ...mockRequest, body: { oldPassword: 'OldPassword123!', newPassword: 'NewPassword123!' }, user: mockRequestDotUserObject };
    let resultFromUserService: IResponse<null> = { success: true, data: null, message: 'Password changed successfully', statusCode: 200 };

    it('should change user password successfully', async () => {
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockUserService.ChangePassword.mockResolvedValue(resultFromUserService);

      await userController.ChangePassword(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Change password controller');
      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Change password controller');
      expect(mockUserService.ChangePassword).toHaveBeenCalledWith(expect.objectContaining({ ...mockRequest.body, email: mockRequestDotUserObject.email }));
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromUserService, mockResponse, 'Change password controller');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors during password change', async () => {
      mockRequest = { ...mockRequest, body: { oldPassword: 'OldPassword123!', newPassword: 'NewPassword123!' }, user: mockRequestDotUserObject };
      resultFromUserService = { success: false, data: null, message: 'Invalid old password', statusCode: 400 };

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockUserService.ChangePassword.mockResolvedValue(resultFromUserService);

      await userController.ChangePassword(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext
      );

      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Change password controller');
      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Change password controller');
      expect(mockUserService.ChangePassword).toHaveBeenCalledWith(expect.objectContaining({ ...mockRequest.body, email: mockRequestDotUserObject.email }));
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromUserService, mockResponse, 'Change password controller');
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError(resultFromUserService.message, resultFromUserService.statusCode, 'Change password controller'));
    });
  });
});