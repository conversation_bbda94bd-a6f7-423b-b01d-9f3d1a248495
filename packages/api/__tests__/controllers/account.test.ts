import { NextFunction, Response } from 'express';
import { AccountController } from '../../v1/controllers';
import { AccountService } from '../../v1/services';
import { ControllerHelper, IRequest, IResponse, Role } from '../../utils';
import { Account, IDelete, IGenerateAccountData } from '../../models';
import { ApiError } from '../../utils';
import * as ACCOUNT from '../../__mocks__/accountMock';

jest.mock('../../v1/services/account.service');
jest.mock('../../utils/controllerHelpers');



describe('AccountController', () => {
  let accountController: AccountController;
  let mockAccountService: jest.Mocked<AccountService>;
  let mockControllerHelper: jest.Mocked<ControllerHelper>;
  let mockRequest: Partial<IRequest>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock<NextFunction>;
  let mockBasePayload: {type: Role.USER, iat: number, exp: number, isAuthenticated: boolean, id?: string };

  beforeEach(() => {
    jest.clearAllMocks();

    const controllerHelper = new ControllerHelper();
    const accountService = new AccountService(new Account());

    mockAccountService = {
      Create: jest.fn(accountService.Create),
      Update: jest.fn(accountService.Update),
      Delete: jest.fn(accountService.Delete),
    } as any;

    mockControllerHelper = {
      validateRequestBody: jest.fn(controllerHelper.validateRequestBody),
      handleServiceResult: jest.fn(controllerHelper.handleServiceResult),
      validateParameterId: jest.fn(controllerHelper.validateParameterId),
      validateQueryParams: jest.fn(controllerHelper.validateQueryParams),
      validateUser: jest.fn(controllerHelper.validateUser),
    } as any;

    accountController = new AccountController(
      mockAccountService as AccountService,
      mockControllerHelper as ControllerHelper
    );

    mockBasePayload = { type: Role.USER, iat: *********, exp: *********, isAuthenticated: true };

    mockRequest = {
      params: {},
      query: {},
      user: undefined,
      body: {},
    };

    mockResponse = {
      statusCode: 200,
      json: jest.fn(),
    };

    mockNext = jest.fn();
  });

  describe('.Create', () => {
    let mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1231', email: '<EMAIL>' };
    mockRequest = { ...mockRequest, body: { usersCount: 1}, user: mockRequestDotUserObject };
    const data: IGenerateAccountData = { ...ACCOUNT.MockGeneralAccountDataEmpty  };
    let resultFromAccountService = { 
      success: true, 
      data: { ...data, usersCount: 1, ownerId: mockRequestDotUserObject.id },
      message: 'Account created successfully', 
      statusCode: 201 };

    it('should create account successfully', async () => {
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockAccountService.Create.mockResolvedValue(resultFromAccountService);

      await accountController.Create(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Create account controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Create account controller');
      expect(mockAccountService.Create).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockRequest.body, ownerId: mockRequestDotUserObject.id })
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromAccountService, mockResponse, 'Create account controller');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors during account creation e.g if user is already an account owner', async () => {
      resultFromAccountService = { success: false, data: null, message: 'You are already an account owner.', statusCode: 400 };
      
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockAccountService.Create.mockResolvedValue(resultFromAccountService);

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await accountController.Create(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Create account controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Create account controller');
      expect(mockAccountService.Create).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockRequest.body, ownerId: mockRequestDotUserObject.id })
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        expect.objectContaining(resultFromAccountService),
        mockResponse,
        'Create account controller'
      );

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError('You are already an account owner.', 400, 'Create account controller'));
    });
  });

  describe('.Update', () => {
    let mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1231', email: '<EMAIL>' };
    mockRequest = { ...mockRequest, body: { usersCount: 1000}, params: { accountId: ACCOUNT.MockGeneralAccountDataEmpty.id }, user: mockRequestDotUserObject };
    const mockAccountData = { ...ACCOUNT.MockGeneralAccountDataEmpty, usersCount: 1000 };
    const resultFromAccountService = { success: true, data: mockAccountData, message: 'Account updated successfully', statusCode: 200 };

    it('should update account successfully', async () => {
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockAccountService.Update.mockResolvedValue(resultFromAccountService);

      await accountController.Update(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Update account controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Update account controller');
      expect(mockAccountService.Update).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockRequest.body, id: mockRequest.params.accountId, ownerId: mockRequestDotUserObject.id })
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromAccountService, mockResponse, 'Update account controller');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle validation errors during account update e.g invalid id', async () => {
      // invalid mongodb id
      mockRequest = { ...mockRequest, params: { accountId: '12345' }, body: {companiesCount: 10} };
      
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockAccountService.Update.mockResolvedValue({
        success: false,
        data: null,
        message: 'Validation failed, Invalid Id',
        statusCode: 400,
      });

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await accountController.Update(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Update account controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Update account controller');
      expect(mockAccountService.Update).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockRequest.body, id: mockRequest.params.accountId, ownerId: mockRequestDotUserObject.id })
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          data: null,
          message: 'Validation failed, Invalid Id',
          statusCode: 400,
        }),
        mockResponse,
        'Update account controller'
      );

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError('Validation failed, Invalid Id', 400, 'Update account controller'));
    });
  });

  describe('.Delete', () => {
    let mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1231', email: '<EMAIL>' };
    mockRequest = { ...mockRequest, body: undefined, params: { accountId: ACCOUNT.MockGeneralAccountDataEmpty.id }, user: mockRequestDotUserObject };
    const resultFromAccountService: IResponse<IDelete> = { success: true, data: null, message: 'Account deleted successfully', statusCode: 200 };

    it('should delete account successfully', async () => {
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockAccountService.Delete.mockResolvedValue(resultFromAccountService);

      await accountController.Delete(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Delete account controller');
      expect(mockAccountService.Delete).toHaveBeenCalledWith(
        expect.objectContaining({ id: mockRequest.params.accountId, ownerId: mockRequestDotUserObject.id })
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromAccountService, mockResponse, 'Delete account controller');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors during account deletion e.g  user is not an account owner', async () => {
      mockRequestDotUserObject = { ...mockBasePayload, id: '65cfd5e8a8f3b20d4c9b1230', email: '<EMAIL>' };
      mockRequest = { ...mockRequest,
        params: { accountId: ACCOUNT.MockGeneralAccountDataEmpty.id },
        user: mockRequestDotUserObject
      };
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockAccountService.Delete.mockResolvedValue({
        success: false,
        data: null,
        message: 'Unable to delete account, account not found',
        statusCode: 404,
      });

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await accountController.Delete(
        mockRequest as IRequest,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockAccountService.Delete).toHaveBeenCalledWith(
        expect.objectContaining({ id: mockRequest.params.accountId, ownerId: mockRequestDotUserObject.id })
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          data: null,
          message: 'Unable to delete account, account not found',
          statusCode: 404,
        }),
        mockResponse,
        'Delete account controller'
      );

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError('Unable to delete account, account not found', 404, 'Delete account controller'));
    });
  });
});