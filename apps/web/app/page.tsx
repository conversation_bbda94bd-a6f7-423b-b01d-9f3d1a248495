"use client";

import Chat from "@sparkstrand/chat";
import { ChatProvider } from "@sparkstrand/chat-api-client/context";
import { SocketClientOptions } from "@sparkstrand/chat-api-client/types";
import GroupChat from "./components/GroupChat";

export default function Home() {
  const chatOptions: SocketClientOptions = {
    url: process.env.NEXT_PUBLIC_CHAT_SERVER_URL || "",
    apiKey: process.env.NEXT_PUBLIC_CHAT_API_KEY || "",
    id: `685a87a7314d311976466ac5`, // Unique guest ID for the user - which can be the userId on your platform or the assigned userId from the sparkstrand-chat-server(assuming the guest is just logining in)
    autoConnect: false,
    debug: true, // Made sure you set this to false in production
  };
  const myCustomTheme = {
    bgColor: "#1A1A1A",
    hoverColor: "#FF5722",
    textColor: "#FEFEFE",
    borderColor: "#CCCCCC33",
    activeChatBorder: "rgb(47 165 102)",
    accentColor: "rgb(39 37 37)",
    secondaryColor: "rgb(47 165 102)",
    height: "100vh",
  };


  return (
    <ChatProvider options={chatOptions}>
      <GroupChat chatUserId="chatUserId" />
    </ChatProvider>
  );
}
