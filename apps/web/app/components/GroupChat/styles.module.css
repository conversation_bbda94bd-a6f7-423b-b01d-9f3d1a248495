.group__chat {
  width: 100%;
  padding: 24px 32px;
  min-height: 100vh;
  background: #121212;
}

.group__chat_header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group__chat_header h2 {
  font-family: Antonio, sans-serif !important;
  font-weight: 400;
  font-size: 28px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #ffffff;
}

.group__chat_search {
  width: 269px;
  height: 42px;
  gap: 8px;
  padding: 6px 16px;
  border: 1px solid #ffffff26;
  background: #ffffff0d;
  display: flex;
  align-items: center;
  gap: 12px;
  border-radius: 12px;
}

.group__chat_search input {
  border: none;
  outline: none;
  flex: 1;
  height: 100%;
  background: transparent;
}

.group__chat_search input:hover,
.group__chat_search input:focus,
.group__chat_search input:active {
  border: none;
  outline: none;
  box-shadow: none; /* Ensures no shadow appears */
}

.group__chat_content {
  width: 80%;
  margin: 32px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.group__chat_pickers {
  width: 70%;
  height: 45px;
  border-radius: 12px;
  background: #f0f0f0;
  box-sizing: border-box;
  padding: 4px;
  display: flex;
  margin-bottom: 32px;
}

.group__chat_picker {
  width: 50%;
  height: 100%;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000000;
  transition: all 0.3s ease-in;
  cursor: pointer;
}

.picker__active {
  background: #2c7c51;
  color: #ffffff;
}

.chat__count {
  width: 100%;
  padding: 4px 0;
  border-bottom: 1px solid #d9d9d9;
  font-family: Bricolage Grotesque;
  font-weight: 500;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 5%;
  color: #ffffff;
  margin-bottom: 24px;
}

.chats {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat {
  width: 100%;
  height: 149px;
  padding: 6px;
  border-radius: 12px;
  display: flex;
  gap: 32px;
  align-items: center;
}

.chat:hover {
  border: 1px solid #2c7c51;
}

.chat__image {
  width: 160px;
  height: 118px;
  border-radius: 12px;
}

.chat__image img {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: cover;
}

.chat__details {
  flex: 1;
  height: 100%;
  background: #ffffff05;
  border: 1px solid #ffffff0d;
  border-radius: 12px;
  padding: 13px 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chat__details_top {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.chat__details_top h4 {
  font-family: Bricolage Grotesque;
  font-weight: 600;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 5%;
  color: #ffffff;
}

.chat__details_top h3 {
  font-family: Bricolage Grotesque;
  font-weight: 700;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 5%;
  color: #ffffff;
}

.remark {
  display: flex;
  gap: 6px;
  align-items: flex-end;
}

.remark p {
  font-family: Bricolage Grotesque;
  font-weight: 600;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 5%;
  color: #ffffff;
}

.chat__details_buttom {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat__details_buttom button {
  padding: 2px 4px;
  border: none;
  outline: none;
  border-bottom: 1px solid #ffffff;
  color: #ffffff;
  font-family: Bricolage Grotesque;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 5%;
  cursor: pointer;
}

.attendies {
  display: flex;
  align-items: center;
}

.attendies__images {
  display: flex;
  align-items: center;
}

.attendies__image {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #fff;
  padding: 1px;
}

.attendies__image:nth-child(1) {
  transform: translateX(0);
}

.attendies__image:nth-child(2) {
  transform: translateX(-10px);
}
.attendies__image:nth-child(3) {
  transform: translateX(-20px);
}
.attendies__image:nth-child(4) {
  transform: translateX(-30px);
}
.attendies__image:nth-child(5) {
  transform: translateX(-40px);
}

.attendies__image img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.attendies p {
  font-family: Bricolage Grotesque;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 5%;
  color: #ffffff;
  transform: translateX(-20px);
}

.pagination {
  width: 100%;
  display: flex;
  list-style: none;
  gap: 24px;
  padding: 0;
  justify-content: space-between;
  margin-top: 32px;
}

.previous {
  margin-right: auto;
  height: 29px;
  border-radius: 4px;
  border-width: 1px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease-in;
}

.next {
  margin-left: auto;
  height: 29px;
  border-radius: 4px;
  border-width: 1px;
  padding: 4px 8px;
  border: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  transition: all 0.3s ease-in;
}

.previous:hover,
.next:hover {
  background: #2c7c51;
}

.paginate__active {
  background: #2c7c51;
  width: 25px;
  height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
}

.page__item {
  width: 25px;
  height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
}

.page__item:hover {
  background: #2c7c51;
}

@media (max-width: 820px) {
  .group__chat {
    padding: 0;
  }

  .chat {
    padding: 0;
  }
  .group__chat_content {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .chat {
    height: unset;
    gap: 12px;
  }

  .chat__image {
    width: 120px;
    height: 118px;
    border-radius: 12px;
  }

  .chat__details {
    padding: 8px;
    gap: 8px;
  }

  .chat__details_top h4 {
    font-family: Bricolage Grotesque;
    font-weight: 600;
    font-size: 12px;
    line-height: 100%;
    letter-spacing: 5%;
    color: #ffffff;
  }

  .chat__details_top h3 {
    font-family: Bricolage Grotesque;
    font-weight: 700;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 5%;
    color: #ffffff;
  }

  .remark {
    display: flex;
    gap: 6px;
    align-items: flex-end;
  }

  .remark {
    gap: 4px;
    align-items: center;
    font-size: 10px;
  }

  .remark p {
    font-family: Bricolage Grotesque;
    font-weight: 600;
    font-size: 10px;
    line-height: 100%;
    letter-spacing: 5%;
    color: #ffffff;
  }

  .chat__details_buttom {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .chat__details_buttom button {
    font-size: 10px;
    order: 2
  }
}
