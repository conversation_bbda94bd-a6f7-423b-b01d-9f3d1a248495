
const CustomPagination = ({
  currentPage,
  totalPages,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (currentPage: number) => void;
}) => {
  const MAX_VISIBLE_PAGES = 5; // Show max 5 page numbers at once

  const getPageNumbers = () => {
    if (totalPages <= MAX_VISIBLE_PAGES) {
      return Array.from({ length: totalPages }, (_, i) => i);
    }

    const startPage = Math.max(
      0,
      currentPage - Math.floor(MAX_VISIBLE_PAGES / 2)
    );
    const endPage = Math.min(totalPages - 1, startPage + MAX_VISIBLE_PAGES - 1);

    const pages = [];

    // Always show first page
    pages.push(0);

    // Add ellipsis if needed after first page
    if (startPage > 1) {
      pages.push("left-ellipsis");
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      if (i > 0 && i < totalPages - 1) {
        pages.push(i);
      }
    }

    // Add ellipsis if needed before last page
    if (endPage < totalPages - 2) {
      pages.push("right-ellipsis");
    }

    // Always show last page
    pages.push(totalPages - 1);

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="w-full flex gap-2 md:gap-6 list-none p-0 justify-between mt-8 text-white">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 0}
        className={`
            mr-auto h-[29px] rounded border border-[#f0f0f0] px-2 
            flex items-center transition-all duration-300 
            ${
              currentPage === 0
                ? "opacity-50 cursor-not-allowed"
                : "opacity-100 hover:bg-[#2c7c51] cursor-pointer"
            }
          `}
      >
        Previous
      </button>

      <div className="flex gap-2">
        {pageNumbers.map((page, index) => {
          if (typeof page === "string") {
            return (
              <span
                key={index}
                className="w-[25px] h-[29px] flex justify-center items-center"
              >
                ...
              </span>
            );
          }

          return (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`
                  w-[25px] h-[29px] flex justify-center items-center 
                  rounded-md transition-all duration-300
                  ${
                    currentPage === page
                      ? "bg-[#2c7c51] cursor-pointer"
                      : "hover:bg-[#2c7c51] cursor-pointer"
                  }
                `}
            >
              {page + 1}
            </button>
          );
        })}
      </div>

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages - 1}
        className={`
            ml-auto h-[29px] rounded border border-[#f0f0f0] px-2 py-1
            flex items-center transition-all duration-300
            ${
              currentPage >= totalPages - 1
                ? "opacity-50 cursor-not-allowed"
                : "opacity-100 hover:bg-[#2c7c51] cursor-pointer"
            }
          `}
      >
        Next
      </button>
    </div>
  );
};

export default CustomPagination;