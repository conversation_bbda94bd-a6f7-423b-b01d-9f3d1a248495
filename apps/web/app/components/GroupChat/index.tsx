"use client";

import React, { useEffect, useMemo, useState } from "react";
import styles from "./styles.module.css";
import { IoSearch } from "react-icons/io5";
import { LuMapPin } from "react-icons/lu";
import Image from "next/image";
import { useChat, useChatRoom } from "@sparkstrand/chat-api-client/context";
import CustomPagination from "./CustomPagination";
import moment from "moment";
import Chat from "@sparkstrand/chat";
import { SocketClientOptions } from "@sparkstrand/chat-api-client/types";

const fallbackImage =
  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='%23ddd'%3E%3Crect width='100' height='100'/%3E%3C/svg%3E";

const GroupChat: React.FC<{ chatUserId: string }> = ({ chatUserId }) => {
  const [pickerType, setPickerType] = useState<"Past" | "Upcoming">("Past");
  // const { theme } = useTheme() || {
  //   theme: { textColor: "#ffffff", secondaryColor: "#2c7c51" },
  // };
  const { rooms = [], emitGetListOfGuestRooms } = useChatRoom() || {};
  const [roomID, setRoomID] = useState("");

  const { isConnected, login, userId } = useChat();
  const roomsPerPage = 3;
  const [currentPage, setCurrentPage] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined" && emitGetListOfGuestRooms) {
      emitGetListOfGuestRooms();
    }
  }, []);

  const { filteredRooms, pastLength, upcomingLength } = useMemo(() => {
    const currentTime = Date.now();
    const filtered = rooms
      .reverse()
      .filter((room) =>
        room?.name?.toLowerCase().includes(searchQuery.toLowerCase())
      );

    const past = filtered.filter(
      (room) =>
        room?.metaData?.startDate &&
        new Date(room.metaData.startDate).getTime() < currentTime
    );

    const upcoming = filtered.filter(
      (room) =>
        room?.metaData?.startDate &&
        new Date(room.metaData.startDate).getTime() > currentTime
    );

    return {
      filteredRooms: pickerType === "Past" ? past : upcoming,
      pastLength: past.length,
      upcomingLength: upcoming.length,
    };
  }, [rooms, searchQuery, pickerType]);

  const totalPages = Math.max(
    1,
    Math.ceil(filteredRooms.length / roomsPerPage)
  );
  const roomsToShow = useMemo(() => {
    return filteredRooms.slice(
      currentPage * roomsPerPage,
      (currentPage + 1) * roomsPerPage
    );
  }, [filteredRooms, currentPage]);

  const getSafeImageUrl = (url: string | undefined) => {
    return url || fallbackImage;
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = fallbackImage;
  };

  const loginUser = async () => {
    if (userId && !isConnected) {
      await login(userId).catch((err) =>
        console.log("Login failed:*****", err)
      );
    }
  };

  useEffect(() => {
    console.log(chatUserId);

    if (chatUserId) {
      loginUser();
      console.log("****Login loop***");
    }
  }, [chatUserId, userId]);
  const myCustomTheme = {
    bgColor: "#1A1A1A",
    hoverColor: "#FF5722",
    textColor: "#FEFEFE",
    borderColor: "#CCCCCC33",
    activeChatBorder: "rgb(47 165 102)",
    accentColor: "rgb(39 37 37)",
    secondaryColor: "rgb(47 165 102)",
    height: "100vh",
  };

  console.log(filteredRooms);
  const chatOptions: SocketClientOptions = {
    url: process.env.NEXT_PUBLIC_CHAT_SERVER_URL || "",
    apiKey: process.env.NEXT_PUBLIC_CHAT_API_KEY || "",
    id: `685a87a7314d311976466ac5`, // Unique guest ID for the user - which can be the userId on your platform or the assigned userId from the sparkstrand-chat-server(assuming the guest is just logining in)
    autoConnect: true,
    debug: true, // Made sure you set this to false in production
  };

  return (
    <div className={styles.group__chat}>
      {roomID && (
        <div
          className="fixed top-0 left-0 h-screen w-screen z-10000 backdrop-blur-2xl"
          style={{ background: myCustomTheme.bgColor }}
        >
          <Chat
            chatOptions={chatOptions}
            isFull
            onClose={() => setRoomID("")}
            theme={myCustomTheme}
            roomID={roomID}
          />
        </div>
      )}
      <div className={styles.group__chat_header}>
        <h2>Groups</h2>
        <div className={styles.group__chat_search}>
          <IoSearch color="#fffff" size={20} />
          <input
            type="text"
            name=""
            id=""
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      <div className={styles.group__chat_content}>
        <div className="w-full md:w-[70%] h-[45px] bg-gray-100 rounded-xl flex p-1 mb-8">
          {["Past", "Upcoming"].map((type) => (
            <button
              key={type}
              type="button"
              onClick={() => {
                setPickerType(type as "Past" | "Upcoming");
                setCurrentPage(0);
              }}
              className={`w-1/2 h-full flex justify-center items-center rounded-xl cursor-pointer transition-all duration-300 ${
                pickerType === type ? "bg-green-700 text-white" : "text-black"
              }`}
            >
              {type} Events
            </button>
          ))}
        </div>
        <div className="w-full border-b border-gray-300 text-white text-sm font-medium pb-1 mb-6">
          {pickerType} Events (
          {pickerType === "Past" ? pastLength : upcomingLength})
        </div>
        {roomsToShow.length === 0 ? (
          <div className="w-full text-center text-white py-10">
            No {pickerType.toLowerCase()} events found
          </div>
        ) : (
          <div className={styles.chats}>
            {roomsToShow?.map((room, index) => {
              const guests = [
                ...(room?.guests?.admins || []),
                ...(room?.guests?.members || []),
                ...(room?.guests?.moderators || []),
              ];

              const startDate = room?.metaData?.startDate
                ? moment(room.metaData.startDate).format("MMMM Do, YYYY")
                : "Date not specified";
              return (
                <div
                  key={index}
                  className={styles.chat}
                  onClick={() => setRoomID(room.id)}
                >
                  <div className={styles.chat__image}>
                    <Image
                      width={160}
                      height={118}
                      src={getSafeImageUrl(room?.avatar?.fileUrl)}
                      alt="chat image"
                      onError={handleImageError}
                    />
                  </div>
                  <div className={styles.chat__details}>
                    <div className={styles.chat__details_top}>
                      <h4>{startDate}</h4>
                      <h3>{room?.name || "Untitled Group"}</h3>
                      {room?.metaData?.location?.name && (
                        <div className={styles.remark}>
                          <LuMapPin color="#fff" size={16} />
                          <p>{room.metaData.location.name}</p>
                        </div>
                      )}
                    </div>
                    <div className={styles.chat__details_buttom}>
                      <button>View Event Room</button>
                      <div className="flex w-fit order-1 md:order-2 md:flex-1 justify-end items-center gap-7">
                        <div className="flex -space-x-2">
                          {guests.slice(0, 5).map(({ avatar }, i) => (
                            <div
                              key={i}
                              className="w-5 h-5 md:w-7 md:h-7 bg-white rounded-full overflow-hidden p-[1px] border border-white"
                            >
                              <img
                                src={getSafeImageUrl(avatar?.fileUrl)}
                                alt="Attendee"
                                className="w-full h-full object-cover rounded-full"
                                onError={handleImageError}
                              />
                            </div>
                          ))}
                        </div>
                        <p className="order-1 md:order-2 text-white text-[10px] md:text-sm transform -translate-x-5">
                          {room?.membersCount || 0} attendee
                          {(room?.membersCount || 0) !== 1 && "s"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
        <div className={styles.pagination}>
          <CustomPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={(page) => setCurrentPage(page)}
          />
        </div>
      </div>
    </div>
  );
};

export default GroupChat;
