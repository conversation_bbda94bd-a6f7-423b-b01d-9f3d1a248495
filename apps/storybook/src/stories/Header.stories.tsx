import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Header from "@sparkstrand/chat";

const meta: Meta<typeof Header> = {
  title: "Components/Page", // Storybook sidebar path
  component: Header,
  tags: ["autodocs"], // Enables automatic documentation
 
};

  const testTheme = {
    bgColor: "#1A1A1A",
    hoverColor: "#FF5722",
    textColor: "#FEFEFE",
    borderColor: "#CCCCCC33",
    activeChatBorder: "rgb(47 165 102)",
    accentColor: "rgb(39 37 37)",
    secondaryColor: "rgb(47 165 102)",
    height: "100vh"
  };

export default meta;

// Story type
type Story = StoryObj<typeof Header>;

// Default story
export const Default: Story = {
  render: () => <Header theme={testTheme} />,
};

