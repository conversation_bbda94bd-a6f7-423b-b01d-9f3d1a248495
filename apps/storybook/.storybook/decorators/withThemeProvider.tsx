// .storybook/decorators/withThemeProvider.tsx
import React from "react";
import { ThemeProvider } from "@sparkstrand/chat";

  const myCustomTheme = {
    bgColor: "#1A1A1A",
    hoverColor: "#FF5722",
    textColor: "#FEFEFE",
    borderColor: "#CCCCCC33",
    activeChatBorder: "rgb(47 165 102)",
    accentColor: "rgb(39 37 37)",
    secondaryColor: "rgb(47 165 102)",
    height: "100vh"
  };

export const withThemeProvider = (Story: any) => (
  <ThemeProvider initialTheme={myCustomTheme}>
    <Story />
  </ThemeProvider>
);
