version: 2.1

orbs:
  node: circleci/node@7.1.0

commands:
  install-dependencies:
    steps:
      - restore_cache:
          keys:
            - yarn-cache-{{ checksum "yarn.lock" }}
      - run:
          name: Install Dependencies (Root)
          command: |
            yarn install --immutable > failure.txt 2>&1
            INSTALL_STATUS=$?
            if [ $INSTALL_STATUS -ne 0 ]; then
              echo "Dependencies Installation failed. See failure.txt for details."
              echo -e "\n❌ Your yarn.lock is out of sync with package.json\n" >> failure.txt
              exit $INSTALL_STATUS
            fi
            echo "Dependencies Installation successful."
      - save_cache:
          key: yarn-cache-{{ checksum "yarn.lock" }}
          paths:
            - ~/.cache/yarn
            - node_modules

  # install-github-cli:
  #   description: Install GitHub CLI
  #   steps:
  #     - run:
  #         name: Install GitHub CLI
  #         command: |
  #           sudo apt-get update && sudo apt-get install -y gh

  install-tools:
    description: Install required tools
    steps:
      - run:
          name: Install Tools
          command: |
            sudo apt-get update
            sudo apt-get install -y jq git

  setup-npm-auth:
    description: Setup NPM authentication
    steps:
      - run:
          name: Configure NPM Authentication
          command: |
            echo "@sparkstrand:registry=https://npm.pkg.github.com/" >> ~/.npmrc
            echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" >> ~/.npmrc
            npm whoami --registry=https://npm.pkg.github.com/ || (echo "NPM authentication failed" && exit 1)

  notify-pipeline-failure:
    description: Notify PR Author or Commit Author About Pipeline Errors
    parameters:
      failure_endpoint:
        type: string
        default: "${FAILURE_NOTIFICATION_URL}"
      failure_file:
        type: string
        default: "failure.txt"
      is_main_branch:
        type: boolean
        default: false
    steps:
      - run:
          name: Notify PR Author or Commit Author About Pipeline Failure
          when: on_fail
          command: |
            if [ "<< parameters.is_main_branch >>" = "true" ]; then
              echo "Setting up failure payload for main branch: $CIRCLE_BRANCH "
              PR_NUMBER=$(curl -s -L \
                -H "Accept: application/vnd.github+json" \
                -H "Authorization: Bearer $NPM_TOKEN" \
                "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls?state=closed&base=main&sort=updated&direction=desc" \
                | jq '[.[] | select(.merged_at != null) | select(.merge_commit_sha == "'"$CIRCLE_SHA1"'")] | first | .number' \
                | tr -d '"')
              { read -r PR_TITLE; read -r PR_BRANCH_NAME; read -r AUTHOR; } < <(
                curl -s -H "Authorization: Bearer $NPM_TOKEN" \
                    -H "Accept: application/vnd.github+json" \
                    "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls/$PR_NUMBER" |
                jq -r '.title // "unknown", .head.ref // "unknown", .user.login // "unknown"'
              )
              echo "***Print merged branch details***"
              echo "PR Number: $PR_NUMBER"
              echo "PR Title: $PR_TITLE"
              echo "PR Branch: $PR_BRANCH_NAME"
              echo "PR Author: $AUTHOR"
            else
              echo "Setting up failure payload for non-main branch: $CIRCLE_BRANCH "
              PR_NUMBER=$(echo "$CIRCLE_PULL_REQUEST" | grep -oE '[0-9]+$' || echo "unknown")
              BRANCH_NAME="$CIRCLE_BRANCH"
              { read -r PR_TITLE; read -r AUTHOR; } < <(
                curl -s -H "Authorization: Bearer $NPM_TOKEN" \
                    -H "Accept: application/vnd.github+json" \
                    "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/commits/$CIRCLE_SHA1" \
                | jq -r '.title // "unknown", .author.login // "unknown"'
              )
              echo "***Print commit details***"
              echo "PR Number: $PR_NUMBER"
              echo "PR Title: $PR_TITLE"
              echo "PR Branch: $BRANCH_NAME"
              echo "PR Author: $AUTHOR"
            fi

            if [ -n "<< parameters.failure_endpoint >>" ]; then
              FAILURE_MESSAGE_TITLE="failed-pipeline"
              failure_reason=$(cat << parameters.failure_file >> 2>/dev/null || echo "Unknown error")
              payload=$(jq -n \
                --arg username "$AUTHOR" \
                --arg message "$FAILURE_MESSAGE_TITLE" \
                --arg CIRCLE_PULL_REQUEST "$CIRCLE_PULL_REQUEST" \
                --arg pr_number "$PR_NUMBER" \
                --arg pr_title "$PR_TITLE" \
                --arg last_committer "$AUTHOR" \
                --arg failure_reason "$failure_reason" \
                --arg CIRCLE_BRANCH "$BRANCH_NAME" \
                --arg CIRCLE_PROJECT_REPONAME "$CIRCLE_PROJECT_REPONAME" \
                --arg CIRCLE_PROJECT_USERNAME "$CIRCLE_PROJECT_USERNAME" \
                --arg CIRCLE_REPOSITORY_URL "$CIRCLE_REPOSITORY_URL" \
                --arg CIRCLE_SHA1 "$CIRCLE_SHA1" \
                '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST, PR_TITLE: $pr_title, PR_NUMBER: $pr_number, LAST_COMMITTER_NAME: $last_committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $CIRCLE_BRANCH, CIRCLE_PROJECT_REPONAME: $CIRCLE_PROJECT_REPONAME, CIRCLE_PROJECT_USERNAME: $CIRCLE_PROJECT_USERNAME, CIRCLE_REPOSITORY_URL: $CIRCLE_REPOSITORY_URL, CIRCLE_SHA1: $CIRCLE_SHA1, failure_reason: $failure_reason}}')
              
              echo "Sending failure notification payload: $payload"
              curl -X POST -H 'Content-Type: application/json' --data "$payload" "<< parameters.failure_endpoint >>"
            fi

jobs:
  install-dependencies:
    executor: node/default
    working_directory: ~/chat-application
    steps:
      - checkout
      - install-dependencies
      - notify-pipeline-failure:
          failure_endpoint: "${FAILURE_NOTIFICATION_URL}"
          failure_file: "failure.txt"
          is_main_branch: false

  run-tests-and-typecheck:
    executor: node/default
    parameters:
      is_main_branch:
        type: boolean
        default: false
    working_directory: ~/chat-application
    steps:
      - checkout
      - install-dependencies
      - run:
          name: Generate Prisma Client
          command: cd packages/api && npx prisma generate
      - run:
          name: Run Tests
          command: |
            cd packages/api
            yarn test > ../../failure.txt 2>&1
            TEST_STATUS=$?
            cd -
            if [ $TEST_STATUS -ne 0 ]; then
              echo "Tests failed. See failure.txt for details."
              exit $TEST_STATUS
            fi
            echo "Tests passed."
      - run:
          name: Build All Packages
          command: yarn turbo run build --filter="./packages/*"
      - run:
          name: Run Typecheck
          command: |
            yarn typecheck > failure.txt 2>&1
            TYPECHECK_STATUS=$?
            if [ $TYPECHECK_STATUS -ne 0 ]; then
              echo "Typecheck failed. See failure.txt for details."
              exit $TYPECHECK_STATUS
            fi
            echo "Typecheck passed."
      - notify-pipeline-failure:
          failure_endpoint: "${FAILURE_NOTIFICATION_URL}"
          failure_file: "failure.txt"
          is_main_branch: << parameters.is_main_branch >>

      - store_test_results:
          path: packages/api/test-results

  deploy-api-package-to-render:
    executor: node/default
    working_directory: ~/chat-application
    steps:
      - checkout
      - install-dependencies
      - run:
          name: Deploy API Package to Render
          command: |
            echo "Deploying API Package to Render"
            echo "Successfully Deployed API Package to Render"

  release-packages:
    docker:
      - image: cimg/node:18.19
    parameters:
      is_main_branch:
        type: boolean
        default: false
    steps:
      - checkout
      - install-tools
      # - install-github-cli
      - run:
          name: Setup Yarn
          command: |
            corepack enable
            corepack prepare yarn@4.6.0 --activate
            yarn --version
      - install-dependencies
      - setup-npm-auth
      - run:
          name: Configure Git and SSH
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "CircleCI Bot"
            mkdir -p ~/.ssh
            touch ~/.ssh/known_hosts
            ssh-keyscan -t ed25519 github.com >> ~/.ssh/known_hosts
      - run:
          name: Version, Publish, and Prepare Notification
          command: |
            CHAT_PAYLOAD_MESSAGE="None"
            AGENT_PAYLOAD_MESSAGE="None"
            CHAT_API_CLIENT_PAYLOAD_MESSAGE="None"
            TYPES_PAYLOAD_MESSAGE="None"
            CHAT_UI_PAYLOAD_MESSAGE="None"

            if [ "<< parameters.is_main_branch >>" = "true" ]; then
              echo "Processing release for main branch"
              git fetch origin main
              LATEST_COMMIT=$(git rev-parse HEAD)
              PREV_COMMIT=$(git merge-base origin/main HEAD^ 2>/dev/null || git rev-parse origin/main)
              echo "Comparing commits: $PREV_COMMIT -> $LATEST_COMMIT"
              CHANGED_PACKAGES=$(git diff --name-only "$PREV_COMMIT" "$LATEST_COMMIT" | grep '^packages/' | cut -d/ -f2 | sort -u || true)
              
              if [ -z "$CHANGED_PACKAGES" ]; then
                echo "No package changes detected via Git. Skipping release."
                exit 0
              fi
              echo "Changed packages detected via Git:"
              echo "$CHANGED_PACKAGES"

              # Exclude 'api' from publishing
              CHANGED_PACKAGES=$(echo "$CHANGED_PACKAGES" | grep -v '^api$')

              if [ -z "$CHANGED_PACKAGES" ]; then
                echo "No packages to publish after excluding 'api'. Skipping release."
                exit 0
              fi

              # Clean up feature releases
              echo "Detecting branch that was merged into main..."
              PR_NUMBER=$(curl -s -L \
                -H "Accept: application/vnd.github+json" \
                -H "Authorization: Bearer $NPM_TOKEN" \
                "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls?state=closed&base=main&sort=updated&direction=desc" \
                | jq '[.[] | select(.merged_at != null) | select(.merge_commit_sha == "'"$CIRCLE_SHA1"'")] | first | .number' \
                | tr -d '"')
              if [ -z "$PR_NUMBER" ] || [ "$PR_NUMBER" = "null" ]; then
                echo "No matching merged PR found. Falling back to parsing merge commit."
                BRANCH_NAME=$(git log -1 --pretty=format:'%s' | grep -oE "Merge pull request #[0-9]+ from [^ ]+" | awk '{print $NF}' | sed 's|.*/||')
              else
                echo "Found PR #$PR_NUMBER"
                BRANCH_NAME=$(curl -s -H "Authorization: Bearer $NPM_TOKEN" \
                  -H "Accept: application/vnd.github+json" \
                  "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls/$PR_NUMBER" \
                  | jq -r '.head.ref' || echo "")
              fi
              if [ -z "$BRANCH_NAME" ]; then
                echo "Could not determine source branch name. Skipping feature release deletion."
              else
                echo "Detected merged branch: $BRANCH_NAME"
                for package in $CHANGED_PACKAGES; do
                  PACKAGE_NAME="$package"
                  echo "Fetching package versions for $PACKAGE_NAME..."
                  VERSIONS_JSON=$(curl -s -H "Authorization: Bearer ${NPM_TOKEN}" \
                    -H "Accept: application/vnd.github+json" \
                    "https://api.github.com/orgs/sparkstrand/packages/npm/$PACKAGE_NAME/versions")
                  FEATURE_VERSION_IDS=$(echo "$VERSIONS_JSON" | jq -r ".[] | select(.name | test(\"-$BRANCH_NAME-[0-9]+$\")) | .id")
                  if [ -n "$FEATURE_VERSION_IDS" ]; then
                    echo "Found feature release IDs for $PACKAGE_NAME from branch $BRANCH_NAME:"
                    echo "$FEATURE_VERSION_IDS"
                    echo "$FEATURE_VERSION_IDS" | while read -r VERSION_ID; do
                      if [ -n "$VERSION_ID" ]; then
                        echo "Deleting package version $PACKAGE_NAME (ID: $VERSION_ID)"
                        curl -X DELETE -v -H "Authorization: Bearer ${NPM_TOKEN}" \
                          -H "Accept: application/vnd.github+json" \
                          "https://api.github.com/orgs/sparkstrand/packages/npm/$PACKAGE_NAME/versions/$VERSION_ID"
                      fi
                    done
                  else
                    echo "No feature releases found for $PACKAGE_NAME from branch $BRANCH_NAME"
                  fi
                done
              fi

              # Build all packages
              echo "Building all packages before merging into main"
              yarn turbo run build --filter="./packages/*" > failure.txt 2>&1
              BUILD_STATUS=$?
              if [ $BUILD_STATUS -ne 0 ]; then
                echo "Build failed. See failure.txt for details."
                exit $BUILD_STATUS
              fi
              echo "Build passed."

              # Version, publish, and prepare notification payload
              for package in $CHANGED_PACKAGES; do
                LATEST_VERSION=$(npm view @sparkstrand/$package versions --json --registry=https://npm.pkg.github.com/ \
                  | jq -r '.[] | select(test("^[0-9]+\\.[0-9]+\\.[0-9]+$"))' | sort -V | tail -n 1 || echo "0.0.0")
                NEW_VERSION=$(echo "$LATEST_VERSION" | awk -F. '{$NF+=1; print $1"."$2"."$NF}')
                echo "Bumping $package from $LATEST_VERSION to $NEW_VERSION"
                jq --arg version "$NEW_VERSION" '.version = $version' "packages/$package/package.json" > tmp.json && mv tmp.json "packages/$package/package.json"
                cd "packages/$package"
                npm publish --registry=https://npm.pkg.github.com/
                cd -
                MESSAGE="@sparkstrand/$package@$NEW_VERSION"
                if [ "$package" = "agent" ]; then
                  AGENT_PAYLOAD_MESSAGE="$MESSAGE"
                elif [ "$package" = "chat-api-client" ]; then
                  CHAT_API_CLIENT_PAYLOAD_MESSAGE="$MESSAGE"
                elif [ "$package" = "types" ]; then
                  TYPES_PAYLOAD_MESSAGE="$MESSAGE"
                elif [ "$package" = "chat-ui" ]; then
                  CHAT_UI_PAYLOAD_MESSAGE="$MESSAGE"
                else
                  CHAT_PAYLOAD_MESSAGE="$MESSAGE"
                fi
              done

              RESPONSE=$(curl -s -w "%{http_code}" -o response.json \
                -H "Authorization: token $NPM_TOKEN" \
                "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls/$PR_NUMBER")

              if [ "$RESPONSE" = "200" ]; then
                PR_AUTHOR=$(jq -r '.user.login // "unknown"' response.json)
              else
                PR_AUTHOR="unknown"
              fi

              # Commit and push changes
              git add .
              if git diff --quiet && git diff --staged --quiet; then
                echo "No changes to commit or push."
              else
                git commit -m "chore(release): publish stable versions from main branch by @$PR_AUTHOR via PR #$PR_NUMBER merged [skip ci]" || echo "Nothing to commit"
                git push origin main || echo "Failed to push to main"
                git push origin --tags || echo "Failed to push tags"
              fi
                     

              JSON_PAYLOAD=$(jq -n \
                --arg pr_number "$PR_NUMBER" \
                --arg branch_name "main" \
                --arg repo_name "$CIRCLE_PROJECT_REPONAME" \
                --arg repo_owner "$CIRCLE_PROJECT_USERNAME" \
                --arg author "$PR_AUTHOR" \
                --arg agent_payload_message "$AGENT_PAYLOAD_MESSAGE" \
                --arg chat_api_client_payload_message "$CHAT_API_CLIENT_PAYLOAD_MESSAGE" \
                --arg types_payload_message "$TYPES_PAYLOAD_MESSAGE" \
                --arg chat_ui_payload_message "$CHAT_UI_PAYLOAD_MESSAGE" \
                --arg chat_payload_message "$CHAT_PAYLOAD_MESSAGE" \
                '{pr_number: $pr_number, branch_name: $branch_name, repo_name: $repo_name, repo_owner: $repo_owner, author: $author, agent_payload_message: $agent_payload_message, chat_api_client_payload_message: $chat_api_client_payload_message, types_payload_message: $types_payload_message, chat_ui_payload_message: $chat_ui_payload_message, chat_payload_message: $chat_payload_message}'
              )

            else
              echo "Processing snapshot release for feature branch: $CIRCLE_BRANCH"
              git fetch --depth=2 origin "$CIRCLE_BRANCH"
              LATEST_COMMIT=$(git rev-parse HEAD)
              PREV_COMMIT=$(git rev-parse HEAD^ 2>/dev/null || git merge-base origin/main HEAD)
              echo "Comparing commits: $PREV_COMMIT -> $LATEST_COMMIT"
              CHANGED_PACKAGES=$(git diff --name-only "$PREV_COMMIT" "$LATEST_COMMIT" | grep '^packages/' | cut -d/ -f2 | sort -u || true)

              if [ -z "$CHANGED_PACKAGES" ]; then
                echo "No packages changed. Skipping snapshot release."
                exit 0
              fi
              echo "Detected changed packages:"
              echo "$CHANGED_PACKAGES"

              # Exclude 'api' from publishing
              CHANGED_PACKAGES=$(echo "$CHANGED_PACKAGES" | grep -v '^api$')

              # NUM_CHANGED=$(echo "$CHANGED_PACKAGES" | grep -c .)
              # echo "Number of changed packages: $NUM_CHANGED"
              
              # if [ "$NUM_CHANGED" -eq 1 ] && echo "$CHANGED_PACKAGES" | grep -q '^api$'; then
              #   echo "'api' is the only changed package. Skipping snapshot release."
              #   exit 0
              # fi

              if [ -z "$CHANGED_PACKAGES" ]; then
                echo "No packages to publish after excluding 'api'. Skipping snapshot release."
                exit 0
              fi

              # TIMESTAMP=$(date +%s)
              # SNAPSHOT_VERSION="0.0.0-${CIRCLE_BRANCH}-${TIMESTAMP}"
              # echo "Using snapshot version: $SNAPSHOT_VERSION"

              for package in $CHANGED_PACKAGES; do
                echo "Creating snapshot for $package"
                LATEST_VERSION=$(npm view @sparkstrand/$package versions --json --registry=https://npm.pkg.github.com/ \
                  | jq -r '.[]' \
                  | grep -E '^[0-9]+\.[0-9]+\.[0-9]+$' \
                  | sort -V \
                  | tail -n 1 || echo "0.0.0")
                TIMESTAMP=$(date +%s)
                SNAPSHOT_VERSION="${LATEST_VERSION}-${CIRCLE_BRANCH}-${TIMESTAMP}"
                echo "Using snapshot version: $SNAPSHOT_VERSION"
                jq --arg version "$SNAPSHOT_VERSION" '.version = $version' "packages/$package/package.json" > tmp.json && mv tmp.json "packages/$package/package.json"
                echo "Updated version in packages/$package/package.json: $(jq -r '.version' packages/$package/package.json)"
                yarn turbo run build --filter="./packages/$package" > failure.txt 2>&1
                BUILD_STATUS=$?
                if [ $BUILD_STATUS -ne 0 ]; then
                  echo "Build failed. See failure.txt for details."
                  exit $BUILD_STATUS
                fi
                cd "packages/$package"
                npm publish --registry=https://npm.pkg.github.com/
                cd -
                MESSAGE="@sparkstrand/$package@$SNAPSHOT_VERSION"

                if [ "$package" = "agent" ]; then
                  AGENT_PAYLOAD_MESSAGE="$MESSAGE"
                elif [ "$package" = "chat-api-client" ]; then
                  CHAT_API_CLIENT_PAYLOAD_MESSAGE="$MESSAGE"
                elif [ "$package" = "types" ]; then
                  TYPES_PAYLOAD_MESSAGE="$MESSAGE"
                elif [ "$package" = "chat-ui" ]; then
                  CHAT_UI_PAYLOAD_MESSAGE="$MESSAGE"
                else
                  CHAT_PAYLOAD_MESSAGE="$MESSAGE"
                fi
              done

              PR_NUMBER=$(echo "$CIRCLE_PULL_REQUEST" | grep -oE '[0-9]+$' || echo "unknown")
              COMMIT_RESPONSE=$(curl -s -w "%{http_code}" -o commit.json \
                -H "Authorization: token $NPM_TOKEN" \
                "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/commits/$CIRCLE_SHA1")

              if [ "$COMMIT_RESPONSE" = "200" ]; then
                COMMIT_AUTHOR=$(jq -r '.author.login // "unknown"' commit.json)
              else
                COMMIT_AUTHOR="unknown"
              fi

              JSON_PAYLOAD=$(jq -n \
                --arg pr_number "$PR_NUMBER" \
                --arg branch_name "$CIRCLE_BRANCH" \
                --arg repo_name "$CIRCLE_PROJECT_REPONAME" \
                --arg repo_owner "$CIRCLE_PROJECT_USERNAME" \
                --arg author "$COMMIT_AUTHOR" \
                --arg agent_payload_message "$AGENT_PAYLOAD_MESSAGE" \
                --arg chat_api_client_payload_message "$CHAT_API_CLIENT_PAYLOAD_MESSAGE" \
                --arg types_payload_message "$TYPES_PAYLOAD_MESSAGE" \
                --arg chat_ui_payload_message "$CHAT_UI_PAYLOAD_MESSAGE" \
                --arg chat_payload_message "$CHAT_PAYLOAD_MESSAGE" \
                '{
                  pr_number: $pr_number, 
                  branch_name: $branch_name, 
                  repo_name: $repo_name, 
                  repo_owner: $repo_owner, 
                  author: $author,
                  agent_payload_message: $agent_payload_message,
                  chat_api_client_payload_message: $chat_api_client_payload_message,
                  types_payload_message: $types_payload_message,
                  chat_ui_payload_message: $chat_ui_payload_message,
                  chat_payload_message: $chat_payload_message
                }'
              )
            fi

            echo "export NOTIFICATION_PAYLOAD='$(echo "$JSON_PAYLOAD" | jq -c .)'" >> "$BASH_ENV"
            source "$BASH_ENV"
      - run:
          name: Notify PR Author or Committer About Pipeline Success
          command: |
            if [ -n "$NOTIFICATION_PAYLOAD" ]; then
              echo "Sending NotificationPayload..."
              echo "$NOTIFICATION_PAYLOAD"
              if [ "<< parameters.is_main_branch >>" = "true" ]; then
                NOTIFICATION_URL="https://sparkstrand-github-app.onrender.com/chat-app/main-release/notification"
              else
                NOTIFICATION_URL="https://sparkstrand-github-app.onrender.com/chat-app/feature-release/notification"
              fi
              curl -X POST -H "Content-Type: application/json" -d "$NOTIFICATION_PAYLOAD" "$NOTIFICATION_URL" || echo "Failed to send notification"
            else
              echo "NotificationPayload is empty or not set. Skipping notification."
            fi
      - notify-pipeline-failure:
          failure_endpoint: "${FAILURE_NOTIFICATION_URL}"
          failure_file: "failure.txt"
          is_main_branch: << parameters.is_main_branch >>

workflows:
  version: 2
  Validate-Release-and-Deploy:
    jobs:
      - install-dependencies:
          name: Install Dependencies
      - run-tests-and-typecheck:
          name: Run Tests And Typecheck (Feature)
          is_main_branch: false
          requires:
            - Install Dependencies
          filters:
            branches:
              ignore: main
      - run-tests-and-typecheck:
          name: Run Tests And Typecheck (Main)
          is_main_branch: true
          requires:
            - Install Dependencies
          filters:
            branches:
              only: main
      - release-packages:
          name: Release Packages (Feature)
          is_main_branch: false
          requires:
            - Run Tests And Typecheck (Feature)
          filters:
            branches:
              ignore: main
      - deploy-api-package-to-render:
          name: Deploy-API-Package-To-Render (Main)
          requires:
            - Run Tests And Typecheck (Main)
          filters:
            branches:
              only: main
      - release-packages:
          name: Release Packages (Main)
          is_main_branch: true
          requires:
            - Deploy-API-Package-To-Render (Main)
          filters:
            branches:
              only: main
