#!/usr/bin/env bash
set -euo pipefail

# List of vulnerable packages
VULN_PACKAGES="ansi-regex|ansi-styles|backslash|chalk|chalk-template|color|color-convert|color-name|color-string|debug|error-ex|has-ansi|is-arrayish|proto-tinker-wc|simple-swizzle|slice-ansi|strip-ansi|supports-color|supports-hyperlinks|wrap-ansi"

scan_repo() {
  local repo_path=$1
  echo "🔍 Scanning $repo_path..."

  cd "$repo_path"

  if [ -f "package-lock.json" ]; then
    echo "   Using npm..."
    npm ls --all 2>/dev/null | grep -E "$VULN_PACKAGES" || echo "   ✅ No affected packages"
  elif [ -f "yarn.lock" ]; then
    echo "   Using yarn..."
    if yarn --version | grep -q '^1'; then
      # Yarn v1
      yarn list --pattern "$VULN_PACKAGES" || echo "   ✅ No affected packages"
    else
      # Yarn v2/v3 (<PERSON>)
      yarn npm list --recursive 2>/dev/null | grep -E "$VULN_PACKAGES" || echo "   ✅ No affected packages"
    fi
  elif [ -f "pnpm-lock.yaml" ]; then
    echo "   Using pnpm..."
    pnpm list --depth Infinity 2>/dev/null | grep -E "$VULN_PACKAGES" || echo "   ✅ No affected packages"
  else
    echo "   ⚠️ No lockfile found, skipping"
  fi

  cd - >/dev/null
}

# First scan the root project
scan_repo "."

# Then scan each subfolder (excluding node_modules)
for dir in */ ; do
  [[ "$dir" == "node_modules/" ]] && continue
  [ -d "$dir" ] && scan_repo "$dir"
done
